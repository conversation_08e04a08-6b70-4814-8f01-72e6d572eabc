{"name": "twitter-engine", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "test": "jest", "test:coverage": "jest --coverage"}, "author": "apoorv aron", "license": "ISC", "dependencies": {"@google-cloud/functions-framework": "^3.4.5", "@google-cloud/pubsub": "^4.10.0", "@google-cloud/storage": "^6.10.0", "@google/generative-ai": "^0.24.0", "axios": "^1.8.0", "cheerio": "^1.0.0", "child_process": "^1.0.2", "crawlee": "^3.12.2", "dotenv": "^16.4.7", "form-data": "^4.0.0", "fs": "0.0.1-security", "he": "^1.2.0", "html-entities": "^2.3.3", "image-size": "^1.0.1", "node-fetch": "^2.6.7", "path": "^0.12.7", "pg": "^8.13.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0"}}