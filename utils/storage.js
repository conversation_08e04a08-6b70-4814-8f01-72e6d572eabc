const { Storage } = require("@google-cloud/storage");
const path = require("path");
const fs = require("fs");
const { BUCKET_NAME, BUCKET_FOLDER, CDN_URL } = require("./constants");
const getLogger = require('./logger');

const logger = getLogger();
const storage = new Storage();
const bucket = storage.bucket(BUCKET_NAME);

async function uploadImageToGCS(imageUrl, articleId) {
    logger.debug(`[uploadImageToGCS] Starting upload for articleId=${articleId}, imageUrl=${imageUrl}`);

    try {
        // Step 1: Download image from remote URL
        const response = await fetch(imageUrl);
        const buffer = await response.arrayBuffer();

        const contentType = response.headers.get("content-type");
        logger.debug(`[uploadImageToGCS] Downloaded image, Content-Type=${contentType}, Byte Length=${buffer.byteLength}`);

        // Step 2: Extract filename from image URL
        const originalFilename = path.basename(new URL(imageUrl).pathname);
        logger.debug(`[uploadImageToGCS] Original filename: ${originalFilename}`);

        // Step 3: Encode filename safely for storage
        const safeFilename = encodeURIComponent(originalFilename);
        const fileName = `${BUCKET_FOLDER}/${articleId}-${safeFilename}`;
        logger.debug(`[uploadImageToGCS] Safe GCS filename: ${fileName}`);

        // Step 4: Prepare file handle for upload
        const file = bucket.file(fileName);

        // Step 5: Upload to GCS
        await file.save(Buffer.from(buffer), {
            metadata: { contentType }
        });

        const finalUrl = `${CDN_URL}${BUCKET_FOLDER}/${articleId}-${safeFilename}`;
        logger.info(`[uploadImageToGCS] Upload successful. GCS URL: ${finalUrl}`);

        return finalUrl;
    } catch (error) {
        logger.error(`[uploadImageToGCS] Failed to upload image for articleId=${articleId}, imageUrl=${imageUrl}`);
        logger.error(`[uploadImageToGCS] Error details: ${error.message}`);
        return null;
    }
}

module.exports = { uploadImageToGCS };