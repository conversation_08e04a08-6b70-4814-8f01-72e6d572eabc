const { uploadImageToGCS } = require('./storage');
const {
    ALLOWED_EXTENSIONS,
    MIN_WIDTH,
    MIN_HEIGHT,
    MIN_AREA,
    MIN_ASPECT_RATIO,
    MAX_ASPECT_RATIO
} = require('./constants');
const imageSize = require('image-size');
const axios = require('axios');
const getLogger = require('./logger');
const logger = getLogger();

/**
 * Get dimensions of an image.
 */
async function getImageDimensions(imageUrl) {
    logger.debug(`[getImageDimensions] Fetching image dimensions for URL: ${imageUrl}`);
    try {
        const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
        const dimensions = imageSize(response.data);
        logger.debug(`[getImageDimensions] Dimensions: width=${dimensions.width}, height=${dimensions.height}`);
        return dimensions;
    } catch (error) {
        logger.warn(`[getImageDimensions] Failed for URL: ${imageUrl} | Error: ${error.message}`);
        return null;
    }
}

/**
 * Find the most relevant image based on resolution and ratio.
 */
async function getMostRelevantImage(imageUrls) {
    logger.debug(`[getMostRelevantImage] Received imageUrls: ${JSON.stringify(imageUrls)}`);

    if (!imageUrls || imageUrls.length === 0) {
        logger.debug(`[getMostRelevantImage] No images to evaluate.`);
        return null;
    }

    let bestImage = null;
    let maxArea = 0;

    for (const url of imageUrls) {
        const cleanedUrl = url.split('?')[0];
        logger.debug(`[getMostRelevantImage] Checking URL: ${cleanedUrl}`);

        if (!ALLOWED_EXTENSIONS.test(cleanedUrl)) {
            logger.warn(`[getMostRelevantImage] Skipping due to extension: ${cleanedUrl}`);
            continue;
        }

        const dimensions = await getImageDimensions(cleanedUrl);
        if (!dimensions) {
            logger.debug(`[getMostRelevantImage] Skipping due to missing dimensions: ${cleanedUrl}`);
            continue;
        }

        const { width, height } = dimensions;
        const area = width * height;
        const aspectRatio = width / height;

        logger.debug(`[getMostRelevantImage] Evaluating: width=${width}, height=${height}, area=${area}, aspectRatio=${aspectRatio.toFixed(2)}`);

        if (width < MIN_WIDTH || height < MIN_HEIGHT || area < MIN_AREA) {
            logger.warn(`[getMostRelevantImage] Skipping due to small size: ${cleanedUrl}`);
            continue;
        }

        if (aspectRatio < MIN_ASPECT_RATIO || aspectRatio > MAX_ASPECT_RATIO) {
            logger.warn(`[getMostRelevantImage] Skipping due to bad aspect ratio: ${aspectRatio.toFixed(2)} for URL: ${cleanedUrl}`);
            continue;
        }

        if (area > maxArea) {
            bestImage = cleanedUrl;
            maxArea = area;
            logger.debug(`[getMostRelevantImage] New best image selected: ${bestImage} with area: ${maxArea}`);
        }
    }

    if (bestImage) {
        logger.info(`[getMostRelevantImage] Final selected image: ${bestImage}`);
    } else {
        logger.warn(`[getMostRelevantImage] No relevant image found.`);
    }

    return bestImage;
}

/**
 * Main image processor for tweets.
 */
async function processTweetImages(imageUrls, articleId) {
    logger.debug(`[processTweetImages] Starting for articleId=${articleId}, imageUrls=${JSON.stringify(imageUrls)}`);

    if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
        logger.debug(`[processTweetImages] No images provided for articleId=${articleId}`);
        return [];
    }

    const bestImage = await getMostRelevantImage(imageUrls);
    logger.debug(`[processTweetImages] Best image selected: ${bestImage}`);

    if (!bestImage) {
        logger.debug(`[processTweetImages] No suitable image found for articleId=${articleId}`);
        return [];
    }

    try {
        const storedUrl = await uploadImageToGCS(bestImage, articleId);
        logger.debug(`[processTweetImages] Stored URL: ${storedUrl}`);

        if (storedUrl) {
            logger.info(`[processTweetImages] Successfully uploaded image for articleId=${articleId}: ${storedUrl}`);
            return [storedUrl];
        } else {
            logger.error(`[processTweetImages] Upload failed for image: ${bestImage}`);
            return [];
        }
    } catch (err) {
        logger.error(`[processTweetImages] Exception uploading image: ${bestImage} | Error: ${err.message}`);
        return [];
    }
}

module.exports = { processTweetImages };