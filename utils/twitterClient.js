const axios = require('axios');
const { TWITTER_AUTH_TOKEN, TWITTER_API_BASE, INSERT_TWITTER_PULL_QUERY, GET_LAST_SEEN_TWEET_ID_QUERY, GET_TOTAL_PULLS_SINCE_STARTDATE, MONTHLY_TARGET_PULLS, TWITTER_PULL_START_DAY  } = require('./constants');
const getLogger = require('./logger');
const { getNewsCentreConnection, releaseNewsCentreConnection } = require('./databaseConfig');
const { sendSlackMessage } = require('./slackPublisher');

const logger = getLogger();

// Function to get last seen tweet ID from database
const getLastSeenTweetId = async () => {
    let client;
    try {
        client = await getNewsCentreConnection();
        const result = await client.query(GET_LAST_SEEN_TWEET_ID_QUERY);

        if (result.rows.length > 0) {
            logger.info(`Retrieved lastSeenTweetId from database: ${result.rows[0].last_seen_tweet_id}`);
            return result.rows[0].last_seen_tweet_id;
        } else {
            logger.info('No lastSeenTweetId found in database');
            return null;
        }
    } catch (error) {
        logger.error(`Error retrieving lastSeenTweetId: ${error.message}`);
        return null;
    } finally {
        if (client) {
            releaseNewsCentreConnection(client);
        }
    }
};

// Function to save last seen tweet ID to database
const saveLastSeenTweetIdAndPulls = async (queryIdentifier, tweetsPulledCount, lastSeenTweetId) => {
    let client;
    try {
        if (!lastSeenTweetId) return;

        client = await getNewsCentreConnection();
        await client.query(INSERT_TWITTER_PULL_QUERY, [
            queryIdentifier,
            tweetsPulledCount,
            lastSeenTweetId
        ]);

        logger.info(`Recorded tweet pull & Since_id in DB`);
    } catch (error) {
        logger.error(`Error recording tweet pull: ${error.message}`);
    } finally {
        if (client) {
            releaseNewsCentreConnection(client);
        }
    }
};

// Function to send Slack alert about current month's pulls
const sendMonthlyPullsAlertToSlack = async () => {
    let client;
    try {
        client = await getNewsCentreConnection();

        const todayUTC = new Date();

        // Dynamically determine pull start date in UTC
        let startDate;
        if (todayUTC.getUTCDate() >= TWITTER_PULL_START_DAY) {
            startDate = new Date(Date.UTC(todayUTC.getUTCFullYear(), todayUTC.getUTCMonth(), TWITTER_PULL_START_DAY));
        } else {
            const lastMonth = todayUTC.getUTCMonth() - 1;
            const year = lastMonth < 0 ? todayUTC.getUTCFullYear() - 1 : todayUTC.getUTCFullYear();
            const month = lastMonth < 0 ? 11 : lastMonth;
            startDate = new Date(Date.UTC(year, month, TWITTER_PULL_START_DAY));
        }

        const result = await client.query(GET_TOTAL_PULLS_SINCE_STARTDATE, [startDate]);
        const totalPullsFromDB = Number(result.rows[0]?.total_pulls || 0);
        const remainingPulls = Math.max(0, MONTHLY_TARGET_PULLS - totalPullsFromDB);

        // Calculate reset date (next TWITTER_PULL_START_DAY)
        const resetDateUTC = todayUTC.getUTCDate() >= TWITTER_PULL_START_DAY
            ? new Date(Date.UTC(todayUTC.getUTCFullYear(), todayUTC.getUTCMonth() + 1, TWITTER_PULL_START_DAY))
            : new Date(Date.UTC(todayUTC.getUTCFullYear(), todayUTC.getUTCMonth(), TWITTER_PULL_START_DAY));

        // Format reset date (in UTC)
        const formattedResetDate = resetDateUTC.toISOString().split('T')[0].split('-').reverse().join('/'); // DD/MM/YYYY

        const message = `📈 *Daily Twitter Pull Summary*\n• Pulled so far: *${totalPullsFromDB}*\n• Remaining credits: *${remainingPulls}*\n• Credits reset on: *${formattedResetDate}*`;
        logger.info('Slack Alert Credits Message : ', message);
        await sendSlackMessage(message);
        logger.info(`Slack alert sent: ${message}`);
    } catch (error) {
        logger.error(`Error sending monthly pulls alert to Slack: ${error.message}`);
    } finally {
        if (client) {
            releaseNewsCentreConnection(client);
        }
    }
};

const searchTweets = async (queryParams, maxResults = 10) => {
    try {
        logger.info(`Making Twitter API request with query: ${queryParams}`);

        // Use the query text as an identifier
        const queryIdentifier = queryParams;

        // Get the last seen tweet ID for this specific query
        const lastSeenTweetId = await getLastSeenTweetId();

        // Prepare request parameters
        const requestParams = {
            query: queryParams,
            max_results: maxResults,
            "tweet.fields": "created_at,text",
            "expansions": "author_id,attachments.media_keys",
            "user.fields": "username",
            "media.fields": "media_key,type,url"
        };

        // Add since_id if we have one from a previous call
        if (lastSeenTweetId) {
            logger.info('Last Seen Tweet Id Present');
            requestParams.since_id = lastSeenTweetId;
            logger.info(`Using since_id: ${lastSeenTweetId}`);
        }

        logger.info(`Query Identifier: ${queryIdentifier}`);

        const response = await axios.get(TWITTER_API_BASE, {
            params: requestParams,
            headers: {
                'Authorization': `Bearer ${TWITTER_AUTH_TOKEN}`,
            }
        });

        // Process the response to replace author_id with username and format date
        if (response.data?.data && response.data?.includes?.users) {
            // Create a map of user IDs to usernames
            const userMap = response.data.includes.users.reduce((map, user) => {
                map[user.id] = user.username;
                return map;
            }, {});

            // Create a map of media keys to media URLs
            const mediaMap = (response.data.includes.media || []).reduce((map, media) => {
                if (media.type === 'photo' && media.url) {
                    map[media.media_key] = media.url;
                }
                return map;
            }, {});

            // Transform the data to replace author_id with username and format date
            const processedTweets = response.data.data.map(tweet => {
                // Format date: "April 9, 2025"
                const date = new Date(tweet.created_at);
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                const formattedDate = date.toLocaleDateString('en-US', options);

                // Get image URLs from media keys
                const mediaKeys = tweet.attachments?.media_keys || [];
                const imageUrls = mediaKeys.map(key => mediaMap[key]).filter(Boolean);
                logger.debug(`Tweet ID ${tweet.id} has ${imageUrls.length} image(s): ${imageUrls.join(', ')}`);

                // Return a new object with the transformed properties
                return {
                    id: tweet.id,
                    text: tweet.text,
                    // Replace author_id with username
                    author_id: userMap[tweet.author_id] || tweet.author_id,
                    // Replace created_at with formatted date
                    created_at: formattedDate,
                    // Add image URLs
                    images: imageUrls,
                    // Keep the original data in case it's needed elsewhere
                    original_author_id: tweet.author_id,
                    original_created_at: tweet.created_at
                };
            });

            // Replace the original data array with our processed one
            response.data.data = processedTweets;
            logger.info(`Processed Tweets just after fetching: ${JSON.stringify(processedTweets, null, 2)}`);

            // Update last seen tweet ID if we have results
            if (response.data?.data?.length > 0) {
                // Find highest tweet ID
                const newestTweetId = response.data.data.reduce((maxId, tweet) =>
                    tweet.id > maxId ? tweet.id : maxId, response.data.data[0].id);

                await saveLastSeenTweetIdAndPulls(queryIdentifier, response.data.data.length, newestTweetId);
            }
        }

        logger.info(`Twitter API request successful, received ${response.data?.data?.length || 0} tweets`);
        return response.data;
    } catch (error) {
        // Log detailed error information
        logger.error(`Twitter API Error: ${error.message}`);

        if (error.response) {
            logger.error(`Status code: ${error.response.status}`);
            logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
            logger.error(`Response headers: ${JSON.stringify(error.response.headers)}`);
        } else if (error.request) {
            logger.error('No response received from Twitter API');
            logger.error(`Request details: ${JSON.stringify(error.request)}`);
        } else {
            logger.error(`Request setup error: ${error.message}`);
        }

        // Create and throw an enhanced error
        const enhancedError = new Error(`Twitter API Error: ${error.message}`);
        enhancedError.originalError = error;
        enhancedError.apiDetails = {
            endpoint: TWITTER_API_BASE,
            query: queryParams,
            maxResults
        };

        throw enhancedError;
    }
};




module.exports = { searchTweets, sendMonthlyPullsAlertToSlack };