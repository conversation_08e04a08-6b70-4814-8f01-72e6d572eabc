const { createLogger, format, transports } = require('winston');

const getLogger = () => {
  const logger = createLogger({
    level: 'info',
    format: format.combine(
      format.timestamp(),
      format.printf(({ timestamp, level, message }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${message}`;
      })
    ),
    transports: [
      new transports.Console()
    ],
  });

  return logger;
};

module.exports = getLogger;