const { log, LogLevel } = require('crawlee');
const { searchTweets, sendMonthlyPullsAlertToSlack } = require('./twitterClient');
const { insertNewsArticle, checkArticleExists, getAllCategory, calculatePullsPerInterval } = require('./newsDbManager');
const { publishMessage } = require('./publisher');
const { TAG_TOPIC_NAME, ENVIRONMENT, primaryKeywords, secondaryKeywords, TWITTER_SOURCE_ID, TWITTER_USERNAMES, BANNED_WORDS } = require('./constants');
const { processNewsArticle } = require('./modelIntegrator');
const { sendSlackMessage } = require('./slackPublisher');
const { insertOrUpdateMutedCombo, resetExpiredMutedCombos, getMutedVessels } = require('./mutedComboManager');
const { processTweetImages } = require('./twitterPostImageProcessor');

log.setLevel(LogLevel.INFO);

// Build the query string for Twitter API
const buildQueryString = async () => {
    if (!TWITTER_USERNAMES || TWITTER_USERNAMES.length === 0) {
        throw new Error('No Twitter usernames provided for secondary filtering');
    }

    // Step 1: Start with user query
    let query = `(${TWITTER_USERNAMES.map(username => `from:${username}`).join(' OR ')}) -is:retweet`;

    // Step 2: Get muted vessels to exclude
    const mutedVessels = await getMutedVessels();
    for (const vessel of mutedVessels) {
        const exclusion = `-"${vessel}"`;
        if ((query.length + 1 + exclusion.length) <= 512) {
            query += ` ${exclusion}`;
        } else {
            break; // stop before exceeding 512 characters
        }
    }

    // Step 3: Exclude banned words
    for (const word of BANNED_WORDS) {
        const exclusion = `-${word}`;
        if ((query.length + 1 + exclusion.length) <= 512) {
            query += ` ${exclusion}`;
        } else {
            break;
        }
    }

    return query;
};

// Check if tweet contains at least one keyword from each list
const containsRequiredKeywords = (tweetText) => {
    const lowerText = tweetText.toLowerCase();

    let foundPrimary = primaryKeywords.some(keyword =>
        lowerText.includes(keyword.toLowerCase())
    );

    let foundSecondary = secondaryKeywords.some(keyword =>
        lowerText.includes(keyword.toLowerCase())
    );

    return {
        matches: foundPrimary && foundSecondary,
        primary: primaryKeywords.filter(k => lowerText.includes(k.toLowerCase())),
        secondary: secondaryKeywords.filter(k => lowerText.includes(k.toLowerCase()))
    };
};

// Track keyword combinations and check if they should be muted
const trackKeywordCombination = async (vessel, primary, secondary) => {
    // Insert or update count in DB
    log.info(`[Twitter] Inserting/Updating Values in DB`);
    log.info(`[Twitter] Inserting/Updating Values in DB Vessel Name : ${vessel}`);
    log.info(`[Twitter] Inserting/Updating Values in DB Primary Keyword : ${primary}`);
    log.info(`[Twitter] Inserting/Updating Values in DB Secondary Keyword : ${secondary}`);

    const currentCount = await insertOrUpdateMutedCombo(vessel, primary, secondary);

    if (currentCount > 10) {
        log.info(`[Twitter] Combo muted (count=${currentCount}): ${vessel} + ${primary} + ${secondary}`);
        return { shouldProcess: false };
    }

    return { shouldProcess: true };
};


// Clean up expired muted combinations
const cleanupMutedCombinations = async () => {
    await resetExpiredMutedCombos();
};

// Process a single tweet
const processTweet = async (tweet) => {
    const startTime = Date.now(); // 🚀 Start timing
    try {
        const tweetUrl = `https://twitter.com/user/status/${tweet.id}`;

        // Check if tweet already exists in database
        const exists = await checkArticleExists(tweetUrl);
        if (exists) {
            log.info(`[Twitter] Skipping tweet, already processed: ${tweetUrl}`);
            return 'filtered';
        }

        // Check if tweet contains required keywords
        const keywordCheck = containsRequiredKeywords(tweet.text);
        if (!keywordCheck.matches) {
            log.info(`[Twitter] Skipping tweet, missing required keywords: ${tweetUrl}`);
            return 'filtered';
        }

        log.info('--------------------- Start : Particular Tweet Data ------------------------------------------');
        log.info('Tweet Text: ' + tweet.text);
        log.info('Author Id ' + tweet.author_id);
        log.info('Creation Time' + tweet.created_at);
        log.info("Tweet", tweet);
        log.info('--------------------- End : Particular Tweet Data ------------------------------------------');

        // Upload tweet image if available
        const uploadedImages = await processTweetImages(tweet.images, tweet.id);
        if (!uploadedImages || uploadedImages.length === 0) {
            log.info(`[Twitter] No relevant image found for tweet: ${tweetUrl}`);
        } else {
            log.info(`[Twitter] Uploaded relevant image for tweet: ${tweetUrl}`);
        }

        // Prepare article data
        const articleData = {
            title: "X Post",
            summary: `<p>${tweet.text}</p>`,
            author: tweet.author_id || 'Twitter User',
            publish_date: tweet.created_at || new Date().toISOString(),
            url: tweetUrl,
            source_id: TWITTER_SOURCE_ID,
            images: Array.isArray(uploadedImages) && uploadedImages.length > 0 ? uploadedImages : []
        };

        // Process with model integrator
        log.info(`[Twitter] Invoking Model Integrator for tweet ID: ${tweet.id}`);
        const modelResponses = await processNewsArticle(articleData.title, articleData.summary, tweet.id);
        
        
        if (!Array.isArray(modelResponses) || modelResponses.length === 0) {
            log.error(`[Twitter] Invalid or empty model response for tweet ID: ${tweet.id}`);
            return 'filtered';
        }

        const modelResponse = modelResponses[0];
        log.info(`[Twitter] Full Model Response ${JSON.stringify(modelResponse, null, 2)}`);

        // 🆕 Check relevance field before any other logic
        if (!modelResponse || typeof modelResponse !== 'object') {
            log.error(`[Twitter] Invalid model response for tweet ID: ${tweet.id}`);
            return 'filtered';
        }

        if (modelResponse.is_relevant === false) {
            log.info(`[Twitter] Skipping tweet due to irrelevance results: ${tweetUrl}`);
            return 'filtered';
        }

        if (!Array.isArray(modelResponse.vessels) || modelResponse.vessels.length === 0) {
            log.error(`[Twitter] Model response has no valid vessels for tweet ID: ${tweet.id}`);
            return 'filtered';
        }

        // Extract unique organization objects
        log.info(`[Twitter] Fetching Category for matching`);
        const allValidCategories = await getAllCategory();
        log.info(`[Twitter] Populating All Valid Categories`);
        const validCategoryNames = allValidCategories.map(c => c.name.toLowerCase());

        log.info(`[Twitter] Getting Predicted Categories from Model`);
        const predictedCategories = modelResponse.vessels.flatMap(item => (item.categories || []));
        const matchedCategories = predictedCategories
            .map(cat => (typeof cat === 'object' ? cat : { name: cat, type: 'UNKNOWN' })) // Normalize if string
            .filter(cat => validCategoryNames.includes(cat.name.toLowerCase()));

        log.info(`[Twitter] matchedCategories Length ${matchedCategories.length}`);
        if (matchedCategories.length === 0) {
            log.info(`[Twitter] Skipping tweet due to no matching category: ${tweetUrl}`);
            return 'filtered';
        }

        log.info(`[Twitter] Getting Vessel Names from Model`);
        let vessels = modelResponse.vessels.map(item => item.vessel_name).filter(name => !!name);
        log.info(`[Twitter] Vessel Names from model ${vessels}`);

        // Now check for vessel + primary + secondary muting
        for (const vesselName of vessels) {
            for (const primary of keywordCheck.primary) {
                for (const secondary of keywordCheck.secondary) {
                    const trackResult = await trackKeywordCombination(vesselName, primary, secondary);
                    if (!trackResult.shouldProcess) {
                        log.info(`[Twitter] Skipping tweet due to muted vessel+primary+secondary combo: ${trackResult.combo}`);
                        return 'filtered';
                    }
                }
            }
        }

        // Insert into database AFTER passing muting checks
        log.info(`[Twitter] Inserting tweet: ${tweetUrl}`);
        const articleId = await insertNewsArticle(articleData);
        log.info(`[Twitter] Tweet inserted with ID: ${articleId}`);

        // Process the response (send tags)
        const orgMap = new Map();
        modelResponse.vessels.forEach(item => {
            (item.organizations || []).forEach(org => {
                const orgName = typeof org === 'object' ? org.name : org;
                const orgType = typeof org === 'object' && org.type ? org.type : "UNKNOWN";
                orgMap.set(orgName, { name: orgName, type: orgType });
            });
        });
        const allOrganizations = Array.from(orgMap.values());

        // Extract unique category objects
        const catMap = new Map();
        modelResponse.vessels.forEach(item => {
            (item.categories || []).forEach(cat => {
                const catName = typeof cat === 'object' ? cat.name : cat;
                const catType = typeof cat === 'object' && cat.type ? cat.type : "UNKNOWN";
                catMap.set(catName, { name: catName, type: catType });
            });
        });
        const allCategories = Array.from(catMap.values());

        // Process vessels
        const processedVessels = modelResponse.vessels.map(item => {
            const vesselCategories = (item.categories || []).map(cat =>
                typeof cat === 'object' ? cat.name : cat
            );

            const vesselOrganisations = (item.organizations || []).map(org =>
                typeof org === 'object' ? org.name : org
            );

            return {
            name: item.vessel_name || "",
            imo: item.imo ? parseInt(item.imo, 10) || null : null,
            vesselType: item.vessel_type || "",
            vesselTypeGroup: item.vessel_type_group || "",
                categories: vesselCategories,
                organisations: vesselOrganisations
            };
        }).filter(vessel => vessel.name);

        // Create TagRequest  
        const tagRequest = {
            articleId: articleId,
            newsSourceId: TWITTER_SOURCE_ID,
            organisations: allOrganizations,
            incidentRegions: modelResponse.vessels.flatMap(item => item.region || []),
            vessels: processedVessels,
            categories: allCategories,
            environment: ENVIRONMENT
        };

        //send alerts to user

        log.info(`[Twitter] Publishing TagRequest for tweet ID: ${articleId}`);
        log.info(`[Publishing TagRequest]: ${JSON.stringify(tagRequest, null, 2)}`);
        await publishMessage(TAG_TOPIC_NAME, tagRequest);

        return 'ingested';
    } catch (error) {
        log.error(`[Twitter] Error processing tweet: ${error.message}`);
        return 'filtered';
    } finally {
        const timeTakenSeconds = (Date.now() - startTime) / 1000;
        if (timeTakenSeconds > 120) {
            log.info(`[Twitter] WARNING: Auto-tagging exceeded 2 minutes! Took ${timeTakenSeconds.toFixed(2)} seconds.`);
        } else {
            log.info(`[Twitter] Auto-tagging completed in ${timeTakenSeconds.toFixed(2)} seconds.`);
        }
    }
};

// Main function to fetch and process tweets
const fetchAndProcessTweets = async () => {
    const startTime = Date.now();
    log.info('[Twitter] Starting tweet fetch and process cycle');

    try {
        // Clean up expired muted combinations
        await cleanupMutedCombinations();

        // Build query and fetch tweets
        const queryString = await buildQueryString();
        log.info(`[Twitter] Fetching tweets with query: ${queryString}`);

        const pullsThisCycle = await calculatePullsPerInterval();
        const tweetData = await searchTweets(queryString, pullsThisCycle);

        const nowIST = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
        const hours = nowIST.getHours();
        const minutes = nowIST.getMinutes();

        // Run alert
        if (hours == 10 && minutes == 0) {
            log.info('[Twitter] Sending Slack alert for daily pull summary');
            await sendMonthlyPullsAlertToSlack();
        } else {
            log.info(`[Twitter] ⏳ Skipping Slack alert (Current IST time: ${hours}:${minutes})`);
        }



        if (tweetData && tweetData.data && tweetData.data.length > 0) {
            log.info(`[Twitter] API Found ${tweetData.data.length} tweets`);
            const totalPulled = tweetData?.data?.length || 0;
            let ingested = 0;
            // Send Slack notification
            // await sendSlackMessage(`🚀 Twitter Engine pulled ${tweetData.data.length} tweets successfully.`);
            // Process each tweet
            for (const tweet of tweetData.data) {
                const result = await processTweet(tweet);
                if (result === 'ingested') ingested++;
            }
            const filteredOut = totalPulled - ingested;
            await sendSlackMessage(`📊 *Twitter Pull Summary in this cycle *\n• Tweets Pulled: *${totalPulled}*\n• Ingested: *${ingested}*\n• Filtered Out: *${filteredOut}*`);
        } else {
            log.info('[Twitter] No tweets found matching criteria');

            // New: Send Slack notification for no tweets
            await sendSlackMessage(`⚠️ Twitter Engine pulled 0 tweets this cycle.`);
        }
    } catch (error) {
        log.error(`[Twitter] Error in fetch and process cycle: ${error.message}`);
    }

    log.info(`[Twitter] Completed cycle in ${((Date.now() - startTime) / 1000).toFixed(2)} seconds`);
};

module.exports = { fetchAndProcessTweets, buildQueryString };