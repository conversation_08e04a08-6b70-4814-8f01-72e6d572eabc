const axios = require('axios');
const { SLACK_WEBHOOK_URL } = require('./constants');
const getLogger = require('./logger');

const logger = getLogger();

/**
 * Sends a message to Slack via webhook
 * @param {string} message - Message to send
 */
async function sendSlackMessage(message) {
  if (!SLACK_WEBHOOK_URL) {
    logger.warn('Slack webhook URL is not configured. Skipping Slack notification.');
    return;
  }

  try {
    logger.debug(`Sending Slack message`);
    await axios.post(SLACK_WEBHOOK_URL, {
      text: message,
    });
    logger.info('Successfully sent message to <PERSON><PERSON><PERSON>');
  } catch (error) {
    logger.error(`Failed to send Slack message: ${error.message}`);
  }
}

module.exports = { sendSlackMessage };
