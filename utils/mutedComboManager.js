const { getNewsCentreConnection, releaseNewsCentreConnection } = require('./databaseConfig');
const { INSERT_OR_UPDATE_MUTED_COMBO, RESET_EXPIRED_MUTED_COMBOS, GET_MUTED_VESSELS_QUERY } = require('./constants');
const getLogger = require('./logger');

const logger = getLogger();

/**
 * Insert or update a muted combination in the database
 */
async function insertOrUpdateMutedCombo(vesselName, primaryKeyword, secondaryKeyword) {
    let client;
    try {
        client = await getNewsCentreConnection();
        const result = await client.query(INSERT_OR_UPDATE_MUTED_COMBO, [vesselName, primaryKeyword, secondaryKeyword]);
        return result.rows[0]?.count || 0;
    } catch (error) {
        logger.error(`Error in insertOrUpdateMutedCombo: ${error.message}`);
        throw error;
    } finally {
        if (client) releaseNewsCentreConnection(client);
    }
}

/**
 * Reset muted combinations that expired over 24 hours ago
 */
async function resetExpiredMutedCombos() {
    let client;
    try {
        client = await getNewsCentreConnection();
        await client.query(RESET_EXPIRED_MUTED_COMBOS);
        logger.info('Expired muted combos reset to count = 0');
    } catch (error) {
        logger.error(`Error resetting expired muted combos: ${error.message}`);
        throw error;
    } finally {
        if (client) releaseNewsCentreConnection(client);
    }
}

/**
 * Get list of muted vessels in the last 24 hours with count > 10
 */
async function getMutedVessels() {
    let client;
    try {
        client = await getNewsCentreConnection();
        const result = await client.query(GET_MUTED_VESSELS_QUERY);
        return result.rows.map(row => row.vessel_name);
    } catch (error) {
        logger.error(`Error fetching muted vessels: ${error.message}`);
        throw error;
    } finally {
        if (client) releaseNewsCentreConnection(client);
    }
}

module.exports = {
    insertOrUpdateMutedCombo,
    resetExpiredMutedCombos,
    getMutedVessels
};