const { PubSub } = require('@google-cloud/pubsub');
const {PROJECT_ID} = require('./constants');
const getLogger = require('./logger');

const logger = getLogger();

const pubSubClient = new PubSub({ PROJECT_ID });

async function publishMessage(topicName, data) {
    const dataBuffer = Buffer.from(JSON.stringify(data));
    try {
        logger.info(`Publishing message to topic ${topicName}`);
        const messageId = await pubSubClient.topic(topicName).publishMessage({ data: dataBuffer });
        logger.info(`Message ${messageId} published to topic ${topicName}`);
        return messageId;
    } catch (error) {
        logger.error(`Error publishing message: ${error.message}`);
        throw error;
    }
}

module.exports = { publishMessage };