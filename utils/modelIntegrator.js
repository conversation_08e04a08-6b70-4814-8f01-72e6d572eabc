const fetch = require("node-fetch");
const fs = require("fs");
const path = require("path");
const {
  getAllCategory,
  getAllOrganisation,
  getAllRegion
} = require("./newsDbManager");
const {
  PROMPT_TEMPLATE,
  TEMP_FILE_DIR,
  VESSEL_TYPE,
  VESSEL_TYPE_GROUP,
  API_KEYS
} = require("./constants");
const getLogger = require("./logger");
const { convertToSchema } = require("./responseSchemaConverter");


// Track key usage and rate limits
const keyUsageTracker = {
  currentKeyIndex: 0,
  keyStatuses: API_KEYS.map(() => ({
    isRateLimited: false,
    lastUsed: 0,
    failureCount: 0,
    cooldownUntil: 0
  }))
};

const logger = getLogger();

/**
 * Schema definition for vessel information from model responses
 */
const vesselResponseSchema = {
  type: "object",
  properties: {
    is_relevant: { type: "boolean", default: false },
    vessels: {
      type: "array",
      items: {
        type: "object",
        properties: {
          imo: { type: "string", default: "" },
          vessel_name: { type: "string" },
          vessel_type: { type: "string", default: "Unknown" },
          vessel_type_group: { type: "string", default: "" },
          categories: {
            type: "array",
            items: {
              type: "object",
              properties: {
                name: { type: "string" },
                type: { type: "string", default: "Unknown" },
              },
            },
          },
          region: { type: "array", items: { type: "string" } },
          organizations: {
            type: "array",
            items: {
              type: "object",
              properties: {
                name: { type: "string" },
                type: { type: "string", default: "Unknown" },
              },
            },
          },
        },
      },
    },
  },
};

/**
 * Gets the next available API key
 * @returns {string} - A valid API key to use
 */
function getNextAvailableApiKey() {
  const now = Date.now();
  const tracker = keyUsageTracker;

  // First try the current key if it's not rate limited
  if (!tracker.keyStatuses[tracker.currentKeyIndex].isRateLimited &&
      now > tracker.keyStatuses[tracker.currentKeyIndex].cooldownUntil) {
    return API_KEYS[tracker.currentKeyIndex];
  }

  // Look for an available key
  for (let i = 0; i < API_KEYS.length; i++) {
    const keyIndex = (tracker.currentKeyIndex + i + 1) % API_KEYS.length;
    const keyStatus = tracker.keyStatuses[keyIndex];

    // Reset rate limit status after cooldown period
    if (keyStatus.isRateLimited && now > keyStatus.cooldownUntil) {
      keyStatus.isRateLimited = false;
      keyStatus.failureCount = 0;
    }

    if (!keyStatus.isRateLimited) {
      tracker.currentKeyIndex = keyIndex;
      keyStatus.lastUsed = now;
      return API_KEYS[keyIndex];
    }
  }

  // If all keys are rate limited, use the one with earliest cooldown
  let earliestCooldownIdx = 0;
  let earliestTime = tracker.keyStatuses[0].cooldownUntil;

  for (let i = 1; i < tracker.keyStatuses.length; i++) {
    if (tracker.keyStatuses[i].cooldownUntil < earliestTime) {
      earliestTime = tracker.keyStatuses[i].cooldownUntil;
      earliestCooldownIdx = i;
    }
  }

  // If we're still in cooldown, log the waiting time
  if (now < earliestTime) {
    logger.warn(`All API keys are rate limited. Using key ${earliestCooldownIdx} with shortest cooldown (${Math.round((earliestTime - now)/1000)}s remaining)`);
  }

  tracker.currentKeyIndex = earliestCooldownIdx;
  tracker.keyStatuses[earliestCooldownIdx].lastUsed = now;
  return API_KEYS[earliestCooldownIdx];
}

/**
 * Marks the current API key as rate limited
 * @param {string} articleId - Article identifier for logging
 * @param {number} cooldownSeconds - How long to wait before using this key again
 */
function markCurrentKeyAsRateLimited(articleId, cooldownSeconds = 60) {
  const tracker = keyUsageTracker;
  const currentKeyIndex = tracker.currentKeyIndex;

  // Increment failure count
  tracker.keyStatuses[currentKeyIndex].failureCount++;

  // Exponential backoff based on number of failures
  const backoffMultiplier = Math.pow(2, Math.min(tracker.keyStatuses[currentKeyIndex].failureCount - 1, 5));
  const actualCooldown = cooldownSeconds * backoffMultiplier;

  // Mark key as rate limited with cooldown
  tracker.keyStatuses[currentKeyIndex].isRateLimited = true;
  tracker.keyStatuses[currentKeyIndex].cooldownUntil = Date.now() + (actualCooldown * 1000);

  logger.warn(`[articleId: ${articleId}] API key ${currentKeyIndex} marked as rate limited for ${actualCooldown} seconds due to failure #${tracker.keyStatuses[currentKeyIndex].failureCount}`);

  // Switch to next key
  const nextKey = getNextAvailableApiKey();
  logger.info(`[articleId: ${articleId}] Switched to API key index ${tracker.currentKeyIndex}`);

  return nextKey;
}

/**
 * Extracts JSON from a string response
 * @param {string} responseText - The raw text response
 * @param {string} articleId - Article identifier for logging
 * @returns {Object|Array|null} - Parsed JSON or null if extraction fails
 */
function extractJsonFromText(responseText, articleId) {
  try {
    // Try to extract full JSON object first (includes "is_relevant")
    const objMatch = responseText.match(/\{[\s\S]*\}/);
    if (objMatch) {
      return JSON.parse(objMatch[0]);
    }

    // Then fallback to JSON array
    const jsonMatch = responseText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    // Then try to extract from code block
    const codeBlockMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch) {
      return JSON.parse(codeBlockMatch[1]);
    }

    return null;
  } catch (error) {
    logger.error(`[articleId: ${articleId}] JSON extraction error:`, error);
    return null;
  }
}

/**
 * Converts model response to structured vessel information using schema
 * @param {any} modelResponse - Raw response from model API
 * @param {string} articleId - Article identifier for logging
 * @returns {Object} - Structured vessel information
 */
function convertResponseToSchema(modelResponse, articleId) {
  logger.info(`[articleId: ${articleId}] Converting model response to schema`);

  try {
    // Use the schema converter to convert the response
    const structuredData = convertToSchema(
      modelResponse,
      vesselResponseSchema,
      {
        strictMode: false,
        defaultValues: true,
        removeExtraFields: true,
      }
    );

    if (
      !structuredData ||
      !structuredData.vessels ||
      structuredData.vessels.length === 0
    ) {
      logger.warn(
        `[articleId: ${articleId}] Schema conversion returned empty or invalid result`
      );
      // Fall back to the old normalization method
      const normalizedArray = normalizeModelResponse(modelResponse, articleId);

      // Convert normalized array to schema format if possible
      if (normalizedArray && normalizedArray.length > 0) {
        return {
          vessels: normalizedArray.map((item) => ({
            vessel_name: item.vessel_name || "",
            vessel_type: item.vessel_type || "Unknown",
            vessel_type_group: item.vessel_type_group || "Unknown",
            categories: item.categories || [],
            region: item.region || [],
            organizations: item.organizations || [],
          })),
        };
      }

      // Return minimal valid structure
      return { vessels: [] };
    }

    logger.info(
      `[articleId: ${articleId}] Successfully converted model response to schema with ${structuredData.vessels.length} vessels`
    );
    return structuredData;
  } catch (error) {
    logger.error(
      `[articleId: ${articleId}] Error converting model response to schema: ${error.message}`
    );
    // Return minimal valid structure on error
    return { vessels: [] };
  }
}

/**
 * Normalizes model responses into a consistent array format
 * @param {any} rawResponse - Response from model
 * @param {string} articleId - Article identifier for logging
 * @returns {Array} - Normalized array of response objects
 */
function normalizeModelResponse(rawResponse, articleId) {
  logger.info(`[articleId: ${articleId}] Normalizing model response`);
  logger.info(`[articleId: ${articleId}] Raw response type: ${typeof rawResponse}`);

  // Return empty array for null/undefined responses
  if (!rawResponse) {
    logger.warn(`[articleId: ${articleId}] Raw response is null or undefined`);
    return [];
  }

  try {
    // Case 1: String response
    if (typeof rawResponse === "string") {
      const extractedJson = extractJsonFromText(rawResponse, articleId);
      if (extractedJson) {
        const result = Array.isArray(extractedJson) ? extractedJson : [extractedJson];
        logger.info(`[articleId: ${articleId}] Extracted JSON from string, returning ${result.length} items`);
        return result;
      }
      logger.warn(`[articleId: ${articleId}] Failed to extract JSON from string`);
      return [];
    }

    // Case 2: Gemini API response format
    if (rawResponse.candidates?.[0]?.content?.parts) {
      const responseText = rawResponse.candidates[0].content.parts[0].text;
      logger.info(
        `[articleId: ${articleId}] Processing response text: ${responseText.substring(0, 200)}...`
      );

      const extractedJson = extractJsonFromText(responseText, articleId);
      logger.info(`[articleId: ${articleId}] Extracted JSON from func: ${JSON.stringify(extractedJson, null, 2)}`);
      if (extractedJson) {
        const result = Array.isArray(extractedJson) ? extractedJson : [extractedJson];
        logger.info(`[articleId: ${articleId}] Extracted JSON result from func: ${JSON.stringify(result, null, 2)}`);
        logger.info(`[articleId: ${articleId}] Extracted JSON from response, returning ${result.length} items`);
        return result;
      }
      logger.warn(`[articleId: ${articleId}] Failed to extract JSON from response`);
      return [];
    }

    // Case 3: Already parsed JSON object or array
    if (typeof rawResponse === "object") {
      // Convert object with numeric keys to array
      if (
        !Array.isArray(rawResponse) &&
        Object.keys(rawResponse).every((key) => !isNaN(parseInt(key)))
      ) {
        const result = Object.values(rawResponse);
        logger.info(`[articleId: ${articleId}] Converted object with numeric keys to array, returning ${result.length} items`);
        return result;
      }

      const result = Array.isArray(rawResponse) ? rawResponse : [rawResponse];
      logger.info(`[articleId: ${articleId}] Returning object as-is, ${result.length} items`);
      return result;
    }

    logger.warn(`[articleId: ${articleId}] Unhandled response type: ${typeof rawResponse}`);
    return [];
  } catch (error) {
    logger.error(`[articleId: ${articleId}] Error normalizing response:`, error);
    return [];
  }
}

/**
 * Invokes the primary model (Gemini) with exponential backoff, jitter, and key rotation
 * @param {Object} inputValues - Input data for the model
 * @param {string} articleId - Article identifier for logging
 * @param {Object} options - Options for retry strategy
 * @returns {Object|null} - Raw model response or null on failure
 */
async function invokePrimaryModel(inputValues, articleId, options = {}) {
  const {
    maxRetries = 5,
    baseDelayMs = 1000,
    maxDelayMs = 60000,
    jitterFactor = 0.25,
  } = options;

  // Import the Google Generative AI library
  const { GoogleGenerativeAI } = require("@google/generative-ai");

  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the next available API key
      const apiKey = getNextAvailableApiKey();
      logger.info(`[articleId: ${articleId}] Primary model attempt ${attempt + 1} of ${maxRetries} with key index ${keyUsageTracker.currentKeyIndex}`);

      // Initialize the API with the selected key
      const genAI = new GoogleGenerativeAI(apiKey);

      // Get the generative model
      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash", // Primary model
      });

      // Configure generation parameters
      const generationConfig = {
        temperature: 0.0,
        maxOutputTokens: 8192,
        topP: 1,
        topK: 40,
      };

      // Create the prompt with input values
      const enhancedPrompt = `
${PROMPT_TEMPLATE}

Input:
${JSON.stringify(inputValues, null, 2)}`;

      // Use the generateContent method
      const result = await model.generateContent({
        contents: [{ role: "user", parts: [{ text: enhancedPrompt }] }],
        generationConfig,
      });

      const responseText = result.response.text();

      // Reset failure count for successful key
      keyUsageTracker.keyStatuses[keyUsageTracker.currentKeyIndex].failureCount = 0;

      logger.info(`[articleId: ${articleId}] Primary model request successful, checking response`);
      logger.debug(`[articleId: ${articleId}] Raw response: ${responseText.substring(0, 200)}...`);

      // Try to convert the response using our schema converter
      try {
        const structuredData = convertResponseToSchema(result.response, articleId);

        // If we got a valid structured response, return it in the expected format
        if (structuredData && structuredData.vessels && structuredData.vessels.length > 0) {
          logger.info(`[articleId: ${articleId}] Successfully converted response to schema`);
          return {
            candidates: [
              {
                content: {
                  parts: [{ text: JSON.stringify(structuredData) }],
                },
              },
            ],
          };
        }

        // If schema conversion didn't yield useful results, continue with traditional parsing
        logger.info(`[articleId: ${articleId}] Schema conversion didn't yield useful results, using traditional parsing`);
      } catch (schemaError) {
        logger.warn(`[articleId: ${articleId}] Schema conversion failed: ${schemaError.message}, using traditional parsing`);
      }

      // Check for empty/blank JSON responses
      const isBlankResponse =
        !responseText ||
        responseText.trim() === "" ||
        responseText.trim() === "[]" ||
        responseText.trim() === "{}" ||
        /^\s*(\[\s*\]|\{\s*\})\s*$/.test(responseText);

      if (isBlankResponse) {
        logger.warn(`[articleId: ${articleId}] Received empty or blank JSON response, retrying...`);

        if (attempt < maxRetries - 1) {
          // Try another key if we have multiple keys
          if (API_KEYS.length > 1) {
            markCurrentKeyAsRateLimited(articleId, 30); // Short cooldown for blank responses
          }
          attempt++;
          continue;
        } else {
          return generateDefaultResponse(inputValues, articleId);
        }
      }

      // Try to parse the response as JSON directly
      try {
        const parsedJson = JSON.parse(responseText);

        // Enhanced validation for empty objects or arrays at any nesting level
        const isEmptyJson = (obj) => {
          if (Array.isArray(obj) && obj.length === 0) return true;
          if (typeof obj === "object" && obj !== null && Object.keys(obj).length === 0) return true;

          // Check nested structures
          if (Array.isArray(obj) && obj.length > 0) {
            // If it's an array with a single empty object/array, consider it empty
            if (obj.length === 1 && isEmptyJson(obj[0])) return true;
          }

          if (typeof obj === "object" && obj !== null) {
            // Check if all properties are empty
            const values = Object.values(obj);
            if (values.length > 0 && values.every(isEmptyJson)) return true;
          }

          return false;
        };

        if (isEmptyJson(parsedJson)) {
          logger.warn(`[articleId: ${articleId}] Parsed JSON is empty or contains only empty structures, retrying...`);

          if (attempt < maxRetries - 1) {
            // Try another key if we have multiple keys
            if (API_KEYS.length > 1) {
              markCurrentKeyAsRateLimited(articleId, 30); // Short cooldown for empty responses
            }
            attempt++;
            continue;
          } else {
            return generateDefaultResponse(inputValues, articleId);
          }
        }

        // Valid, non-empty JSON response
        logger.info(`[articleId: ${articleId}] Successfully parsed valid JSON response directly`);

        return {
          candidates: [
            {
              content: {
                parts: [{ text: JSON.stringify(parsedJson) }],
              },
            },
          ],
        };
      } catch (parseError) {
        logger.warn(`[articleId: ${articleId}] Direct JSON parsing failed: ${parseError.message}, trying extraction...`);

        // Extract any JSON-like structure using the extraction function
        const extractedJson = extractJsonFromText(responseText, articleId);
        if (extractedJson) {
          // Check if the extracted JSON is empty
          const isEmptyExtracted =
            (Array.isArray(extractedJson) && extractedJson.length === 0) ||
            (typeof extractedJson === "object" && extractedJson !== null && Object.keys(extractedJson).length === 0);

          if (isEmptyExtracted) {
            logger.warn(`[articleId: ${articleId}] Extracted JSON is empty, retrying...`);

            if (attempt < maxRetries - 1) {
              attempt++;
              continue;
            } else {
              return generateDefaultResponse(inputValues, articleId);
            }
          }

          logger.info(`[articleId: ${articleId}] Successfully extracted non-empty JSON from response`);

          return {
            candidates: [
              {
                content: {
                  parts: [{ text: JSON.stringify(extractedJson) }],
                },
              },
            ],
          };
        }

        // If both direct parsing and extraction failed
        if (attempt < maxRetries - 1) {
          logger.warn(`[articleId: ${articleId}] JSON extraction failed, retrying with clearer instructions...`);
          attempt++;
          continue;
        } else {
          logger.warn(`[articleId: ${articleId}] All parsing attempts failed, using default response`);
          return generateDefaultResponse(inputValues, articleId);
        }
      }
    } catch (error) {
      // Handle rate limit errors with key rotation
      const isRateLimitError =
        error.message &&
        (error.message.includes("Rate limit") ||
         error.message.includes("429") ||
         error.message.includes("quota") ||
         error.message.includes("resource exhausted"));

      logger.error(`[articleId: ${articleId}] Error in primary model (attempt ${attempt + 1}):`, error);

      if (isRateLimitError) {
        // Mark current key as rate limited and get a new one
        markCurrentKeyAsRateLimited(articleId, 60);

        // If we have another key, retry immediately with a small delay
        if (API_KEYS.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay before trying new key
          continue; // Skip incrementing attempt when switching keys due to rate limits
        }
      }

      if (attempt < maxRetries - 1) {
        const delayMs = Math.min(maxDelayMs, baseDelayMs * Math.pow(2, attempt));
        const jitterMs = delayMs * jitterFactor * (Math.random() * 2 - 1);
        const finalDelayMs = Math.max(1000, delayMs + jitterMs);

        logger.info(`[articleId: ${articleId}] Retrying in ${finalDelayMs / 1000} seconds...`);
        await new Promise((resolve) => setTimeout(resolve, finalDelayMs));
        attempt++;
      } else {
        logger.error(`[articleId: ${articleId}] Maximum retry attempts reached`);
        return generateDefaultResponse(inputValues, articleId);
      }
    }
  }

  // If we've exhausted all retries
  logger.error(`[articleId: ${articleId}] All primary model retry attempts failed`);
  return generateDefaultResponse(inputValues, articleId);
}

/**
 * Generates a default response based on input values as a fallback
 * @param {Object} inputValues - Input data for the model
 * @param {string} articleId - Article identifier for logging
 * @returns {Object} - Default response in expected format
 */
function generateDefaultResponse(inputValues, articleId) {
  logger.info(`[articleId: ${articleId}] Generating default response as fallback`);

  try {
    // Extract title and summary from input values
    const { news_title, news_summary } = inputValues;
    const title = news_title || "";
    const summary = news_summary || "";

    // Extract potentially relevant organizations, categories, and regions
    const allOrgs = inputValues.ORGANIZATIONS || [];
    const allCategories = inputValues.CATEGORIES || [];
    const allRegions = inputValues.REGIONS || [];

    // Create a simple default response with minimal matches based on keyword matching
    const defaultResponse = [];

    // Function to check if any keywords from an item match in the text
    const hasMatchingKeywords = (item, text) => {
      if (!item || !item.name || !text) return false;
      const itemName = item.name.toLowerCase();
      const lowercaseText = text.toLowerCase();
      return lowercaseText.includes(itemName);
    };

    // Find matching organizations
    const matchingOrgs = allOrgs
      .filter((org) => hasMatchingKeywords(org, title) || hasMatchingKeywords(org, summary))
      .slice(0, 2); // Limit to 2 matches

    // Find matching categories
    const matchingCategories = allCategories
      .filter((cat) => hasMatchingKeywords(cat, title) || hasMatchingKeywords(cat, summary))
      .slice(0, 2); // Limit to 2 matches

    // Find matching regions
    const matchingRegions = allRegions
      .filter((region) => hasMatchingKeywords(region, title) || hasMatchingKeywords(region, summary))
      .slice(0, 2); // Limit to 2 matches

    // Add at least one default response
    defaultResponse.push({
      organization: matchingOrgs.length > 0 ? matchingOrgs[0].id : null,
      category: matchingCategories.length > 0 ? matchingCategories[0].id : null,
      region: matchingRegions.length > 0 ? matchingRegions[0].id : null,
      confidence: 0.5, // Medium confidence
    });

    // Add additional matches if found
    if (matchingOrgs.length > 1 || matchingCategories.length > 1 || matchingRegions.length > 1) {
      defaultResponse.push({
        organization: matchingOrgs.length > 1 ? matchingOrgs[1].id : matchingOrgs.length > 0 ? matchingOrgs[0].id : null,
        category: matchingCategories.length > 1 ? matchingCategories[1].id : matchingCategories.length > 0 ? matchingCategories[0].id : null,
        region: matchingRegions.length > 1 ? matchingRegions[1].id : matchingRegions.length > 0 ? matchingRegions[0].id : null,
        confidence: 0.4, // Slightly lower confidence
      });
    }

    // Return the default response in the expected format
    const responseJson = JSON.stringify(defaultResponse);
    logger.info(`[articleId: ${articleId}] Generated default response with ${defaultResponse.length} items`);

    return {
      candidates: [
        {
          content: {
            parts: [{ text: responseJson }],
          },
        },
      ],
    };
  } catch (error) {
    logger.error(`[articleId: ${articleId}] Error generating default response:`, error);
    // Return an absolute minimum viable response
    return {
      candidates: [
        {
          content: {
            parts: [{ text: "[]" }],
          },
        },
      ],
    };
  }
}

/**
 * Creates a temporary file to store input values
 * @param {Object} inputValues - Data to store in file
 * @param {string} articleId - Article identifier
 * @returns {Object} - Object containing file path and directory path
 */
function createTemporaryInputFile(inputValues, articleId) {
  const timestamp = new Date().toISOString().replace(/[-:.TZ]/g, "");
  const dirPath = path.join(__dirname, TEMP_FILE_DIR, `${articleId}`);

  // Create directory if it doesn't exist
  fs.mkdirSync(dirPath, { recursive: true });

  const filePath = path.join(dirPath, `prompt_input_values_${timestamp}.json`);
  fs.writeFileSync(filePath, JSON.stringify(inputValues, null, 4));

  logger.info(`[articleId: ${articleId}] Created temporary input file: ${filePath}`);
  return { filePath, dirPath };
}

/**
 * Cleans up temporary files and directories
 * @param {string} filePath - Path to file to remove
 * @param {string} dirPath - Path to directory to check and potentially remove
 * @param {string} articleId - Article identifier for logging
 */
function cleanupTemporaryFiles(filePath, dirPath, articleId) {
  try {
    // Delete the file if it exists
    if (filePath && fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`[articleId: ${articleId}] Deleted temporary file: ${filePath}`);
    }

    // Remove directory if it's empty
    if (dirPath && fs.existsSync(dirPath) && fs.readdirSync(dirPath).length === 0) {
      fs.rmdirSync(dirPath);
      logger.info(`[articleId: ${articleId}] Removed empty directory: ${dirPath}`);
    }
  } catch (error) {
    logger.error(`[articleId: ${articleId}] Error cleaning up temporary files:`, error);
  }
}

/**
 * Main function to process news articles for vessel information
 * @param {string} newsTitle - Title of the news article
 * @param {string} newsSummary - Summary of the news article
 * @param {string} articleId - Unique identifier for the article
 * @returns {Array} - Normalized array of model predictions
 */
async function processNewsArticle(newsTitle, newsSummary, articleId) {
  let filePath = "";
  let dirPath = "";

  try {
    // Step 1: Gather necessary data from database
    logger.info(`[articleId: ${articleId}] Step 1: Gathering data from database`);
    const organizations = await getAllOrganisation();
    const categories = await getAllCategory();
    const regions = await getAllRegion();

    // Step 2: Prepare input values for the model
    logger.info(`[articleId: ${articleId}] Step 2: Preparing input values`);
    const inputValues = {
      ORGANIZATIONS: organizations,
      CATEGORIES: categories,
      REGIONS: regions,
      VESSEL_TYPE: VESSEL_TYPE,
      VESSEL_TYPE_GROUP: VESSEL_TYPE_GROUP,
      news_title: newsTitle,
      news_summary: newsSummary,
    };

    // Step 3: Create temporary input file for traceability
    logger.info(`[articleId: ${articleId}] Step 3: Creating temporary file`);
    const tempFiles = createTemporaryInputFile(inputValues, articleId);
    filePath = tempFiles.filePath;
    dirPath = tempFiles.dirPath;

    // Step 4: Invoke the primary model
    logger.info(`[articleId: ${articleId}] Step 4: Invoking primary model`);
    const rawResponse = await invokePrimaryModel(inputValues, articleId);
    logger.info(`[articleId: ${articleId}] Raw Model Response:`, rawResponse);

    // Step 5: Normalize the response
    logger.info(`[articleId: ${articleId}] Step 5: Normalizing response`);
    const normalizedResponse = normalizeModelResponse(rawResponse, articleId);
    logger.info(`[articleId: ${articleId}] Normalized Response:`, normalizedResponse);
    logger.info(
      `[articleId: ${articleId}] Final normalized response contains ${normalizedResponse.length} items`
    );

    if (normalizedResponse.length === 0) {
      logger.warn(`[articleId: ${articleId}] Warning: Empty response after normalization`);
    }
    
    return normalizedResponse;
  } catch (error) {
    // Handle errors
    logger.error(`[articleId: ${articleId}] Error processing news article:`, error);
    return [];
  } finally {
    // Clean up temporary files
    logger.info(`[articleId: ${articleId}] Cleaning up temporary files`);
    cleanupTemporaryFiles(filePath, dirPath, articleId);
  }
}

/**
 * Health check function to verify API keys status
 * @returns {Object} Status of all API keys
 */
async function checkApiKeysHealth() {
  const results = [];

  for (let i = 0; i < API_KEYS.length; i++) {
    const apiKey = API_KEYS[i];

    try {
      logger.info(`Testing API key ${i + 1} of ${API_KEYS.length}`);

      // Import the Google Generative AI library
      const { GoogleGenerativeAI } = require("@google/generative-ai");

      // Initialize the API with the key
      const genAI = new GoogleGenerativeAI(apiKey);

      // Get the generative model
      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
      });

      // Simple test prompt
      const testPrompt = "Return the following text: 'API key is working'";

      // Use the generateContent method with a timeout
      const startTime = Date.now();
      const result = await Promise.race([
        model.generateContent({
          contents: [{ role: "user", parts: [{ text: testPrompt }] }],
          generationConfig: { temperature: 0.1, maxOutputTokens: 50 },
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Request timeout")), 10000)
        ),
      ]);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const responseText = result.response.text();
      const isWorking = responseText.includes("API key is working");

      results.push({
        keyIndex: i,
        status: isWorking ? "operational" : "degraded",
        responseTime: `${responseTime}ms`,
        error: isWorking ? null : "Unexpected response",
        rateLimited: false,
      });

      logger.info(`API key ${i + 1} is ${isWorking ? "operational" : "degraded"} (${responseTime}ms)`);

    } catch (error) {
      const isRateLimited =
        error.message &&
        (error.message.includes("Rate limit") ||
         error.message.includes("429") ||
         error.message.includes("quota") ||
         error.message.includes("resource exhausted"));

      results.push({
        keyIndex: i,
        status: "error",
        responseTime: null,
        error: error.message,
        rateLimited: isRateLimited,
      });

      logger.error(`API key ${i + 1} test failed: ${error.message}`);

      // Mark the key as rate limited in our tracker if applicable
      if (isRateLimited) {
        keyUsageTracker.keyStatuses[i].isRateLimited = true;
        keyUsageTracker.keyStatuses[i].cooldownUntil = Date.now() + (60 * 1000); // 1 minute cooldown
        keyUsageTracker.keyStatuses[i].failureCount++;
      }
    }

    // Add a small delay between tests to avoid burst rate limits
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Summary count
  const summary = {
    total: API_KEYS.length,
    operational: results.filter(r => r.status === "operational").length,
    degraded: results.filter(r => r.status === "degraded").length,
    error: results.filter(r => r.status === "error").length,
    rateLimited: results.filter(r => r.rateLimited).length
  };

  logger.info(`API key health check summary: ${JSON.stringify(summary)}`);

  return {
    summary,
    details: results,
    timestamp: new Date().toISOString()
  };
}

/**
 * Exports functions for use in other modules
 */

module.exports = {
  processNewsArticle,
  checkApiKeysHealth,
  extractJsonFromText,
  normalizeModelResponse,
  convertResponseToSchema,
  createTemporaryInputFile,
  cleanupTemporaryFiles,
  generateDefaultResponse
};