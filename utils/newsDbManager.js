const { getNewsCentreConnection, releaseNewsCentreConnection } = require('./databaseConfig');
const { GET_NEWS_SOURCE_QUERY, INSERT_NEWS_ARTICLE, CHECK_NEWS_URL_EXISTS_QUERY, GET_ALL_ORGANISATION_QUERY, GET_ALL_CATEGORY_QUERY, GET_ALL_REGION_QUERY, GET_TOTAL_PULLS_SINCE_STARTDATE, MONTHLY_TARGET_PULLS, TWITTER_PULL_START_DAY, INTERVAL_MINUTES } = require('./constants');
const getLogger = require('./logger');

const logger = getLogger();

// Fetches all categories from the database
const getAllCategory = async () => {
  let client;
  try {
    logger.info(`Fetching All Categories`);
    client = await getNewsCentreConnection();
    const result = await client.query(GET_ALL_CATEGORY_QUERY);
    logger.info(`Fetched All Categories`);
    return result.rows.map(row => ({
      name: row.name,
      type: row.type
    }));
  } catch (e) {
    logger.error(`Error in getAllCategoryData: ${e.message}`);
    throw new Error(`Error in getAllCategoryData: ${e.message}`);
  } finally {
    if (client) {
      releaseNewsCentreConnection(client);
    }
  }
};

const getAllOrganisation = async () => {
  let client;
  try {
    logger.info(`Fetching All Organisations`);
    client = await getNewsCentreConnection();
    const result = await client.query(GET_ALL_ORGANISATION_QUERY);
    logger.info(`Fetched All Organisations`);
    return result.rows.map(row => ({
      name: row.name,
      type: row.type
    }));
  } catch (e) {
    logger.error(`Error in getAllOrganisationData: ${e.message}`);
    throw new Error(`Error in getAllOrganisationData: ${e.message}`);
  } finally {
    if (client) {
      releaseNewsCentreConnection(client);
    }
  }
};


const getAllRegion = async () => {
  let client;
  try {
    logger.info(`Fetching All Regions`);
    client = await getNewsCentreConnection();
    const result = await client.query(GET_ALL_REGION_QUERY);
    logger.info(`Fetched All Regions`);
    return result.rows.map(row => row.name);
  } catch (e) {
    logger.error(`Error in getAllRegionNames: ${e.message}`);
    throw new Error(`Error in getAllRegionNames: ${e.message}`);
  } finally {
    if (client) {
      releaseNewsCentreConnection(client);
    }
  }
};

const checkArticleExists = async (url) => {
  let client;
  try {
    client = await getNewsCentreConnection();
    const result = await client.query(CHECK_NEWS_URL_EXISTS_QUERY, [url]);
    return result.rows.length > 0; // Returns true if the article exists, false otherwise
  } catch (e) {
    logger.error(`Error in checkArticleExists: ${e.message}`);
    throw new Error(`Error in checkArticleExists: ${e.message}`);
  } finally {
    if (client) {
      releaseNewsCentreConnection(client);
    }
  }
};

// Inserts a news article into the database with fallback logic
const insertNewsArticle = async (articleData) => {
  let client;
  try {
    logger.info(`Inserting news article into the database. URL: ${articleData.url}`);

    if (!articleData.title || articleData.title.length === 0) {
      throw new Error('Article title cannot be empty');
    }
    if (!articleData.summary || articleData.summary.length === 0) {
      throw new Error('Article summary cannot be empty');
    }

    client = await getNewsCentreConnection();

    const result = await client.query(INSERT_NEWS_ARTICLE, [
      articleData.url,
      articleData.source_id,
      articleData.title,
      articleData.summary,
      articleData.publish_date,
      articleData.author,
      articleData.images,
    ]);

    
    logger.info("----------------Start : Insert Query Debug-----------------");
    logger.info(INSERT_NEWS_ARTICLE);
    logger.info(articleData.url);
    logger.info(articleData.source_id);
    logger.info(articleData.title);
    logger.info(articleData.summary);
    logger.info(articleData.publish_date);
    logger.info(articleData.author);
    // logger.info(articleData.images);
    logger.info("----------------End : Insert Query Debug-----------------");

    return result?.rows?.[0]?.id || null;

  } catch (e) {
    logger.error(`Error in insertNewsArticle: ${e.message}`);
    throw new Error(`Error in insertNewsArticle: ${e.message}`);
  } finally {
    if (client) {
      releaseNewsCentreConnection(client);
    }
  }
};

const calculatePullsPerInterval = async () => {
  logger.info('[Twitter] ⏳ Starting calculation for pulls per interval...');

  // Get today's date in IST
  const today = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  logger.info(`[Twitter] 📅 Today's IST date: ${today.toISOString()}`);

  let startDate;

  // Dynamically decide pull start date
  if (today.getDate() >= TWITTER_PULL_START_DAY) {
      startDate = new Date(today.getFullYear(), today.getMonth(), TWITTER_PULL_START_DAY);
      logger.info(`[Twitter] 📅 Using this month's pull start date: ${startDate.toISOString()}`);
  } else {
      const lastMonth = today.getMonth() - 1;
      const year = lastMonth < 0 ? today.getFullYear() - 1 : today.getFullYear();
      const month = lastMonth < 0 ? 11 : lastMonth;
      startDate = new Date(year, month, TWITTER_PULL_START_DAY);
      logger.info(`[Twitter] 📅 Using last month's pull start date: ${startDate.toISOString()}`);
  }

  let client;
  try {
      client = await getNewsCentreConnection();
      logger.info('[Twitter] 🔗 Connected to News Centre database.');

      // Fetch total pulls done after start date
      const { rows } = await client.query(GET_TOTAL_PULLS_SINCE_STARTDATE, [startDate]);
      const totalPullsFromDB = parseInt(rows[0]?.total_pulls || 0, 10);

      logger.info(`[Twitter] 📊 Total pulls done since start date: ${totalPullsFromDB}`);

      // Calculate remaining pulls
      const restPulls = MONTHLY_TARGET_PULLS - totalPullsFromDB;
      logger.info(`[Twitter] 🎯 Remaining pulls to achieve target (${MONTHLY_TARGET_PULLS}): ${restPulls}`);

      if (restPulls <= 0) {
          logger.info(`[Twitter] ✅ Monthly target achieved. Only minimum 10 pulls will be done per cycle.`);
          return 10; // Minimum pulls if target already achieved
      }

      // Now Calculate END DATE
      let endDate;
      if (today.getDate() >= TWITTER_PULL_START_DAY) {
          if (today.getMonth() === 11) { // December → January next year
              endDate = new Date(today.getFullYear() + 1, 0, TWITTER_PULL_START_DAY);
          } else {
              endDate = new Date(today.getFullYear(), today.getMonth() + 1, TWITTER_PULL_START_DAY);
          }
      } else {
          endDate = new Date(today.getFullYear(), today.getMonth(), TWITTER_PULL_START_DAY);
      }
      logger.info(`[Twitter] 🛫 Calculating pulls until end date: ${endDate.toISOString()}`);

      // Calculate days left
      const daysLeft = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
      logger.info(`[Twitter] 📅 Days left in month: ${daysLeft}`);

      const slotsLeft = daysLeft * (24 * 60 / INTERVAL_MINUTES);
      logger.info(`[Twitter] 🕑 Remaining slots (every ${INTERVAL_MINUTES} minutes): ${slotsLeft}`);

      const pullsPerSlot = Math.ceil(restPulls / slotsLeft);
      const pullsToRequestNow = Math.min(Math.max(10, pullsPerSlot), 100);

      logger.info(`[Twitter] 🚀 Pulls needed per slot: ${pullsPerSlot}`);
      logger.info(`[Twitter] ✅ Final pulls this cycle (between 10 and 100): ${pullsToRequestNow}`);

      return pullsToRequestNow;
  } catch (error) {
      logger.error(`[Twitter] ❌ Error while calculating pulls per interval: ${error.message}`);
      throw error;
  } finally {
      if (client) {
          await releaseNewsCentreConnection(client);
          logger.info('[Twitter] 🔌 Released News Centre DB connection.');
      }
  }
};


module.exports = {
  insertNewsArticle,checkArticleExists,
  getAllCategory,getAllOrganisation,getAllRegion,
  calculatePullsPerInterval
};