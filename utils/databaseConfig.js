const { Pool } = require('pg');
const {
  newsCentreMinConnections,
  newsCentreMaxConnections,
  newsCentreDbUser,
  newsCentreDbPassword,
  newsCentreDbHost,
  newsCentreDbPort,
  newsCentreDbName,
  newsCentreDbSchema,
} = require('./constants');
const getLogger = require('./logger');

const logger = getLogger();

// Connection pool for the News Centre database
let newsCentrePool = null;

/**
 * Get a connection to the News Centre database from the connection pool.
 * This function will create the connection pool if it doesn't already exist.
 */
const getNewsCentreConnection = async () => {
  try {
    // Create the connection pool if it doesn't already exist
    if (!newsCentrePool) {
      logger.info('Creating connection pool for the News Centre database.');
      newsCentrePool = new Pool({
        min: newsCentreMinConnections,
        max: newsCentreMaxConnections,
        user: newsCentreDbUser,
        password: newsCentreDbPassword,
        host: newsCentreDbHost,
        port: newsCentreDbPort,
        database: newsCentreDbName,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // Set the schema for all connections
      newsCentrePool.on('connect', (client) => {
        client.query(`SET search_path TO ${newsCentreDbSchema}`);
      });
    }

    // Get a connection from the pool
    const client = await newsCentrePool.connect();
    logger.debug('Retrieved a connection from the News Centre pool.');
    return client;
  } catch (error) {
    logger.error(`Error while getting connection from News Centre pool: ${error.message}`);
    throw new Error(`Error while getting connection from News Centre pool: ${error.message}`);
  }
};

/**
 * Release a connection back to the News Centre database pool.
 * @param {Object} client - The database client to release.
 */
const releaseNewsCentreConnection = (client) => {
  try {
    if (client) {
      client.release();
      logger.debug('Released the connection back to the News Centre pool.');
    }
  } catch (error) {
    logger.error(`Error while releasing connection to News Centre pool: ${error.message}`);
    throw new Error(`Error while releasing connection to News Centre pool: ${error.message}`);
  }
};

/**
 * Close the News Centre connection pool and release all connections.
 */
const closeNewsCentrePool = async () => {
  try {
    if (newsCentrePool) {
      await newsCentrePool.end();
      logger.info('Closed all connections for the News Centre pool.');
      newsCentrePool = null;
    }
  } catch (error) {
    logger.error(`Error while closing the News Centre connection pool: ${error.message}`);
    throw new Error(`Error while closing the News Centre connection pool: ${error.message}`);
  }
};

module.exports = {
  getNewsCentreConnection,
  releaseNewsCentreConnection,
  closeNewsCentrePool,
};