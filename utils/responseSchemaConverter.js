/**
 * Gemini Response Schema Converter
 *
 * This utility converts Gemini API responses into structured JSON objects
 * based on a provided schema definition.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const getLogger = require("./logger");
const logger = getLogger();

/**
 * Converts a Gemini API response to a structured JSON object based on a schema
 * 
 * @param {Object} geminiResponse - The raw response from Gemini API
 * @param {Object} schema - Schema definition for the expected response structure
 * @param {Object} options - Additional options for conversion
 * @returns {Object} - The structured JSON object
 */
function convertToSchema(geminiResponse, schema, options = {}) {
  try {
    logger.info('Converting Gemini response to schema');
    
    // Extract the text content from the Gemini response
    const responseText = extractTextFromGeminiResponse(geminiResponse);
    if (!responseText) {
      logger.warn('No text content found in Gemini response');
      return null;
    }
    
    // Extract JSON from the response text
    const extractedJson = extractJsonFromText(responseText);
    if (!extractedJson) {
      logger.warn('Failed to extract JSON from Gemini response text');
      return null;
    }
    
    // Validate and transform the extracted JSON according to the schema
    const validatedJson = validateAgainstSchema(extractedJson, schema, options);
    
    logger.info('Successfully converted Gemini response to schema');
    return validatedJson;
  } catch (error) {
    logger.error(`Error converting Gemini response to schema: ${error.message}`);
    return null;
  }
}

/**
 * Extracts text content from a Gemini API response
 * 
 * @param {Object} geminiResponse - The raw response from Gemini API
 * @returns {string|null} - The extracted text or null if extraction fails
 */
function extractTextFromGeminiResponse(geminiResponse) {
  try {
    // Handle different Gemini response formats
    
    // Case 1: String response
    if (typeof geminiResponse === 'string') {
      return geminiResponse;
    }
    
    // Case 2: Gemini API response format
    if (geminiResponse?.candidates?.[0]?.content?.parts) {
      return geminiResponse.candidates[0].content.parts[0].text;
    }
    
    // Case 3: Already parsed JSON object
    if (typeof geminiResponse === 'object' && geminiResponse !== null) {
      return JSON.stringify(geminiResponse);
    }
    
    logger.warn(`Unhandled Gemini response type: ${typeof geminiResponse}`);
    return null;
  } catch (error) {
    logger.error(`Error extracting text from Gemini response: ${error.message}`);
    return null;
  }
}

/**
 * Extracts JSON from a text response robustly
 */
function extractJsonFromText(responseText) {
  try {
    // Clean up any wrapping markdown code block syntax
    const cleaned = responseText
      .replace(/^```(json)?/i, "")
      .replace(/```$/, "")
      .trim();

    // Try to parse directly
    logger.debug("EXTRACT JSON FROM TEXT");
    logger.debug(cleaned);
    logger.debug(JSON.parse(cleaned));
    return JSON.parse(cleaned);
  } catch (directError) {
    try {
      // Try to extract object if direct parsing fails
      const objectMatch = responseText.match(/\{[\s\S]*\}/);
      if (objectMatch) return JSON.parse(objectMatch[0]);

      // Try to extract array
      const arrayMatch = responseText.match(/\[[\s\S]*\]/);
      if (arrayMatch) return JSON.parse(arrayMatch[0]);

      logger.warn("Fallback parsing failed");
      return null;
    } catch (fallbackError) {
      logger.error("JSON extraction error:", fallbackError);
      return null;
    }
  }
}

/**
 * Validates and transforms data according to a schema
 * 
 * @param {Object|Array} data - The data to validate
 * @param {Object} schema - Schema definition
 * @param {Object} options - Additional options for validation
 * @returns {Object|Array} - The validated and transformed data
 */
function validateAgainstSchema(data, schema, options = {}) {
  const {
    strictMode = false,
    defaultValues = true,
    removeExtraFields = true
  } = options;
  
  try {
    // Handle array schema
    if (schema.type === 'array' && schema.items) {
      if (!Array.isArray(data)) {
        if (strictMode) {
          throw new Error(`Expected array but got ${typeof data}`);
        }
        // Try to convert to array if possible
        data = Array.isArray(data) ? data : [data];
      }
      
      return data.map(item => validateAgainstSchema(item, schema.items, options));
    }
    
    // Handle object schema
    if (schema.type === 'object' && schema.properties) {
      if (typeof data !== 'object' || data === null || Array.isArray(data)) {
        if (strictMode) {
          throw new Error(`Expected object but got ${typeof data}`);
        }
        // Use empty object as fallback
        data = {};
      }
      
      const result = {};
      
      // Process each property defined in the schema
      for (const [key, propSchema] of Object.entries(schema.properties)) {
        if (data.hasOwnProperty(key)) {
          // Validate and transform the property value
          result[key] = validateAgainstSchema(data[key], propSchema, options);
        } else if (schema.required && schema.required.includes(key)) {
          // Handle missing required field
          if (strictMode) {
            throw new Error(`Missing required field: ${key}`);
          }
          // Use default value if available
          if (defaultValues && propSchema.default !== undefined) {
            result[key] = propSchema.default;
          } else {
            // Use type-specific default
            result[key] = getDefaultValueForType(propSchema.type);
          }
        } else if (defaultValues && propSchema.default !== undefined) {
          // Use specified default for optional fields
          result[key] = propSchema.default;
        }
      }
      
      // Include non-schema fields if not removing extras
      if (!removeExtraFields) {
        for (const [key, value] of Object.entries(data)) {
          if (!schema.properties.hasOwnProperty(key)) {
            result[key] = value;
          }
        }
      }
      
      return result;
    }
    
    // Handle primitive types
    return validatePrimitiveType(data, schema, options);
  } catch (error) {
    logger.error(`Schema validation error: ${error.message}`);
    if (strictMode) {
      throw error;
    }
    return getDefaultValueForType(schema.type);
  }
}

/**
 * Validates and transforms a primitive value according to its schema
 * 
 * @param {any} value - The value to validate
 * @param {Object} schema - Schema definition for the value
 * @param {Object} options - Additional options for validation
 * @returns {any} - The validated and transformed value
 */
function validatePrimitiveType(value, schema, options = {}) {
  const { strictMode = false, defaultValues = true } = options;
  
  try {
    switch (schema.type) {
      case 'string':
        if (typeof value !== 'string') {
          if (strictMode) {
            throw new Error(`Expected string but got ${typeof value}`);
          }
          // Convert to string if possible
          return value !== null && value !== undefined ? String(value) : 
                 (defaultValues && schema.default !== undefined ? schema.default : '');
        }
        return value;
        
      case 'number':
      case 'integer':
        if (typeof value !== 'number' || (schema.type === 'integer' && !Number.isInteger(value))) {
          if (strictMode) {
            throw new Error(`Expected ${schema.type} but got ${typeof value}`);
          }
          // Try to convert to number
          const num = Number(value);
          if (!isNaN(num)) {
            return schema.type === 'integer' ? Math.round(num) : num;
          }
          return defaultValues && schema.default !== undefined ? schema.default : 0;
        }
        return value;
        
      case 'boolean':
        if (typeof value !== 'boolean') {
          if (strictMode) {
            throw new Error(`Expected boolean but got ${typeof value}`);
          }
          // Convert to boolean
          if (value === 'true' || value === 1 || value === '1') return true;
          if (value === 'false' || value === 0 || value === '0') return false;
          return defaultValues && schema.default !== undefined ? schema.default : false;
        }
        return value;
        
      case 'null':
        return null;
        
      default:
        logger.warn(`Unknown schema type: ${schema.type}`);
        return value;
    }
  } catch (error) {
    logger.error(`Error validating primitive type: ${error.message}`);
    if (strictMode) {
      throw error;
    }
    return defaultValues && schema.default !== undefined ? schema.default : getDefaultValueForType(schema.type);
  }
}

/**
 * Returns a default value for a given type
 * 
 * @param {string} type - The data type
 * @returns {any} - A default value for the type
 */
function getDefaultValueForType(type) {
  switch (type) {
    case 'string': return '';
    case 'number': 
    case 'integer': return 0;
    case 'boolean': return false;
    case 'array': return [];
    case 'object': return {};
    case 'null': return null;
    default: return null;
  }
}

/**
 * Creates a schema definition from a sample object
 * 
 * @param {Object|Array} sample - Sample object to derive schema from
 * @param {Object} options - Options for schema generation
 * @returns {Object} - Generated schema definition
 */
function createSchemaFromSample(sample, options = {}) {
  const {
    makeAllPropertiesRequired = false,
    includeDefaults = true
  } = options;
  
  try {
    // Handle arrays
    if (Array.isArray(sample)) {
      if (sample.length === 0) {
        return {
          type: 'array',
          items: { type: 'object', properties: {} }
        };
      }
      
      // Use the first item as a template
      const itemSchema = createSchemaFromSample(sample[0], options);
      return {
        type: 'array',
        items: itemSchema
      };
    }
    
    // Handle objects
    if (typeof sample === 'object' && sample !== null) {
      const properties = {};
      const required = [];
      
      for (const [key, value] of Object.entries(sample)) {
        properties[key] = createSchemaFromSample(value, options);
        
        if (makeAllPropertiesRequired) {
          required.push(key);
        }
        
        if (includeDefaults) {
          properties[key].default = value;
        }
      }
      
      const schema = {
        type: 'object',
        properties
      };
      
      if (required.length > 0) {
        schema.required = required;
      }
      
      return schema;
    }
    
    // Handle primitive types
    return {
      type: getPrimitiveType(sample),
      ...(includeDefaults ? { default: sample } : {})
    };
  } catch (error) {
    logger.error(`Error creating schema from sample: ${error.message}`);
    return { type: 'object', properties: {} };
  }
}

/**
 * Determines the primitive type of a value
 * 
 * @param {any} value - The value to check
 * @returns {string} - The type name
 */
function getPrimitiveType(value) {
  if (value === null) return 'null';
  if (typeof value === 'number') return Number.isInteger(value) ? 'integer' : 'number';
  return typeof value;
}

module.exports = {
  convertToSchema,
  createSchemaFromSample,
  extractJsonFromText,
  validateAgainstSchema
};
