require('dotenv').config();
// Environment Variables for News Centre Database
const newsCentreMinConnections = parseInt(
  process.env.NEWS_CENTRE_MIN_CONNECTIONS
);
const newsCentreMaxConnections = parseInt(
  process.env.NEWS_CENTRE_MAX_CONNECTIONS
);
const newsCentreDbUser = process.env.NEWS_CENTRE_DB_USER;
const newsCentreDbPassword = process.env.NEWS_CENTRE_DB_PASSWORD;
const newsCentreDbHost = process.env.NEWS_CENTRE_DB_HOST;
const newsCentreDbPort = parseInt(process.env.NEWS_CENTRE_DB_PORT, 10);
const newsCentreDbName = process.env.NEWS_CENTRE_DB_NAME;
const newsCentreDbSchema = process.env.NEWS_CENTRE_DB_SCHEMA;

// Query Constants
const INSERT_NEWS_ARTICLE = `
    WITH new_article AS (
        INSERT INTO articles (url, source_id, title, summary, publish_date, author, images, created_date, updated_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        ON CONFLICT (url) DO NOTHING
        RETURNING id
    )
    SELECT id FROM new_article
    UNION ALL
    SELECT id FROM articles WHERE url = $1 AND NOT EXISTS (SELECT 1 FROM new_article)
    LIMIT 1;
`;

const CHECK_NEWS_URL_EXISTS_QUERY = `
  SELECT id FROM news_centre.articles WHERE url = $1 LIMIT 1;
`;

const GET_ALL_CATEGORY_QUERY = `SELECT name,type FROM news_centre.category`;
const GET_ALL_ORGANISATION_QUERY = `SELECT name,type FROM news_centre.organisation`;
const GET_ALL_REGION_QUERY = `SELECT name FROM news_centre.incident_region`;

const ENVIRONMENT = process.env.ENVIRONMENT;

// Pubsub Constant
const PROJECT_ID = process.env.PROJECT_ID;
const TAG_TOPIC_NAME = process.env.TAG_TOPIC_NAME;
const BUCKET_NAME = process.env.BUCKET_NAME;
const BUCKET_FOLDER = process.env.BUCKET_FOLDER;
const CDN_URL = process.env.CDN_URL;
const VESSEL_TYPE = process.env.VESSEL_TYPE;
const VESSEL_TYPE_GROUP = process.env.VESSEL_TYPE_GROUP;
const GEMINI_API_URL =
  "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
const PROMPT_TEMPLATE = `Vessel Details Extraction Guidelines
Objective
Extract all possible vessel-related details from news articles, focusing on vessel names and their associated information. The output should be a structured JSON format containing the extracted details. If no specific vessel name is found, attempt to extract other relevant maritime-related information from the context.

Input
* Title: {news_title}
* Article Summary: {news_summary}

Output Format
{
  "is_relevant": true,
  "vessels": [
    {{
      "imo": "IMO_number",
      "vessel_name": "Vessel Name",
      "vessel_type": "",
      "vessel_type_group": "Vessel Type",
      "categories": [
      {{ "name": "Category Name1", "type": “Category Type1" }},
      {{ "name": "Category Name2”, "type": "Category Type2” }}
      ],
      "region": [“Region1”, ”Region2”],
      "organizations": [
      {{ "name": “Organisation Name1", "type": "Organisation Type1" }},
      {{ "name": "Organisation Name2”, "type": "Organisation Type1" }}
      ]
    }}
  ]
}

---
**Additional Rule for 'is_relevant' Field:**  

Set "is_relevant": true only if the article clearly and explicitly describes a maritime, marine, or naval emergency or incident involving:
- A named vessel or ship actively involved in an incident
- An event that occurred at sea, on a waterway, in a port, or during maritime transport
- Rescue, collision, explosion, fire, grounding, oil spill, mechanical failure — but only if these are clearly happening in a maritime setting

Important: Just mentioning the word "tanker", "rescue", or "explosion" is NOT enough.

Otherwise, set 'is_relevant': false.
-  This binary decision must be made based on the actual **incident/emergency nature** of the article. 
- It does not mention any specific vessel, or
- It describes an explosion, rescue, or military activity only on land (e.g., borders, cities, military tensions), or
- The incident has no clear connection to maritime operations

---

Extraction Rules
Required Fields
1. IMO Number
    * 7-digit vessel identifier.
    * Use an empty string if unavailable.
    * Example: "imo": "1234567"
2. Vessel Name
    * Extract exact vessel names mentioned in the text.
    * Do NOT include vessels with generic names like:
        * "Vessel 1," "Unknown Vessel," "Unnamed Ship," etc.
    * If no proper vessel name is mentioned, use an empty string.
    * Example: "vessel_name": "GUANGZHOU WENCHONG H2510"
3. Vessel Type
    * Match to a predefined list: {VESSEL_TYPE}.
    * Use contextual clues to determine the type.
    * Example: "vessel_type": "Icebreaker"
4. Vessel Type Group
    * Match to a predefined group: {VESSEL_TYPE_GROUP}.
    * Example: "vessel_type_group": "Tanker"
5. Categories
  * First priority: Match to master data table values from the predefined list: {CATEGORIES}.
  * Use the following deterministic approach for selecting categories:
    - Identify all potential category matches based on keywords in the article
    - Score each potential match by counting explicit keyword occurrences in the text
    - Always select the highest-scoring matches first
    - In case of tied scores, follow the priority order in the Determinism section
  * Categories should never be an empty array.
  * When in doubt, default to broader categories over specific ones to ensure consistency.
6. Region
    * Choose from predefined options: {REGIONS}.
    * Example: "region": ["North America"]
7. Organizations
    * First priority: Match to master data table values from the predefined list: {ORGANIZATIONS}.
    * If none of the predefined options apply, match keywords using context.
    * Organizations should never be an empty array.
    * Example: "organizations": [{{ "name": “Organisation Name1", "type": "Organisation Type1" }},{{ "name": "Organisation Name2”, "type": "Organisation Type1" }} ]
8. Input Preprocessing
  * Before extraction, normalize all input text by:
    - Converting to lowercase
    - Removing punctuation
    - Standardizing vessel type terms (e.g., "tanker", "carrier", "cargo ship")
  * Process the exact same normalized input in the exact same way every time
  * Create and maintain a lookup table of previously processed articles and their extracted data

Error Handling
1. Handle Missing Data:
    * Use an empty string for unavailable required fields.
    * Categories and organizations should never be empty arrays - use context to determine values if needed.
    * If no vessel name is found, do not return an empty array. Instead, use the context of the article to fill in the remaining fields as best as possible.

Best Practices
1. Extract All Names
    * Include every proper vessel name explicitly mentioned in the text.
    * Exclude unnamed or generic references.
2. Accuracy First
    * Extract names and details exactly as mentioned.
    * Use context to classify vessel type or category.
3. Complete Data
    * Fill in all possible fields.
    * Omit entries only when data is truly missing or unclear, and an informed inference cannot be made.
4. Validation
    * Verify data with predefined lists (where applicable).
    * Ensure valid JSON structure.
    * Ensure categories and organizations are never empty arrays.
5. Priority of Data Sources
    * First try to match with the master data tables provided (if any).
    * If no direct match is found, use contextual information to determine appropriate values.
6. Determinism
  * When the input text (title + summary) is exactly the same, always return the exact same vessel_name, imo, vessel_type, vessel_type_group, categories, region, and organizations.
  * If you've processed this exact text before, reuse all previously extracted values exactly.
  * For categories specifically, implement a deterministic selection process:
    - First, look for exact keyword matches between the article and category names
    - When multiple matches exist within the same priority level, select the one that appears first alphabetically
  * Always document your exact categorization logic to ensure future consistency
`;
const TEMP_FILE_DIR = process.env.TEMP_FILE_DIR;
const META_DATA_URL =
  "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=";

// Define keyword lists
const primaryKeywords = [
  "Boat",
  "Bulk Carrier",
  "Bulker",
  "CONRO",
  "Container",
  "Craft",
  "Cruise",
  "Feeder",
  "Ferry",
  "General Cargo",
  "LNG",
  "RORO",
  "Sailing boat",
  "Ship",
  "Tanker",
  "ULCC",
  "Vessel",
  "VLCC",
  "Yacht",
];

const secondaryKeywords = [
  "Abandon",
  "Aground",
  "Allision",
  "Breach",
  "Broken",
  "Casualt",
  "Colli",
  "Disabled",
  "Distress",
  "Drift",
  "Emerg",
  "Explos",
  "Ground",
  "Fire",
  "Ingress",
  "Leak",
  "Mayday",
  "May-day",
  "Oilspill",
  "Pan pan",
  "Pollution",
  "Rescue",
  "SOS",
  "Submerge",
  "Sunk",
  "Tow",
  "Crash",
  "Fuel",
  "Disaster",
  "Struck",
];


const TWITTER_USERNAMES = [
  "mercoglianos",
  "BartGonnissen",
  "CovertShores",
  "IMOHQ",
  "LloydsList",
  "supbrow",
];

const BANNED_WORDS = [
  "damn", "hell", "fuck", "shit"
];

const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;

const INSERT_TWITTER_PULL_QUERY = `
INSERT INTO news_centre.twitter_pulls_tracking (query_identifier, tweets_pulled_count, last_seen_tweet_id, created_at, updated_at)
VALUES ($1, $2, $3, NOW(), NOW())
`;

const GET_LAST_SEEN_TWEET_ID_QUERY = `
SELECT last_seen_tweet_id
FROM news_centre.twitter_pulls_tracking
ORDER BY updated_at DESC
LIMIT 1
`;

const INSERT_OR_UPDATE_MUTED_COMBO = `
  INSERT INTO news_centre.muted_vessel_keyword_combinations (vessel_name, primary_keyword, secondary_keyword, count, updated_at)
  VALUES ($1, $2, $3, 1, NOW())
  ON CONFLICT (vessel_name, primary_keyword, secondary_keyword)
  DO UPDATE 
  SET 
    count = muted_vessel_keyword_combinations.count + 1,
    updated_at = NOW()
  RETURNING count;
`;

const RESET_EXPIRED_MUTED_COMBOS = `
UPDATE news_centre.muted_vessel_keyword_combinations
SET count = 0, updated_at = NOW()
WHERE 
  updated_at < NOW() - INTERVAL '24 hours'
  OR (updated_at < NOW() - INTERVAL '1 hour' AND count <= 10);
`;

const GET_MUTED_VESSELS_QUERY = `
  SELECT DISTINCT vessel_name
  FROM news_centre.muted_vessel_keyword_combinations
  WHERE count > 10
    AND updated_at >= NOW() - INTERVAL '24 hours';
`;

const MONTHLY_TARGET_PULLS = process.env.MONTHLY_TARGET_PULLS;

const TWITTER_PULL_START_DAY = process.env.TWITTER_PULL_START_DAY;

const GET_TOTAL_PULLS_SINCE_STARTDATE = `
SELECT COALESCE(SUM(tweets_pulled_count), 0) AS total_pulls
FROM news_centre.twitter_pulls_tracking
WHERE created_at >= $1;
`;

const API_KEYS = [
  process.env.GEMINI_API_KEY_1,
  process.env.GEMINI_API_KEY_2,
];

const TWITTER_API_BASE = process.env.TWITTER_API_BASE;
const TWITTER_AUTH_TOKEN = process.env.TWITTER_AUTH_TOKEN;
const TWITTER_SOURCE_ID = process.env.TWITTER_SOURCE_ID;
const INTERVAL_MINUTES = process.env.INTERVAL_MINUTES;

// News Source Constants
const ALLOWED_EXTENSIONS = /\.(jpg|jpeg|png)$/i;
// Constants for processing
const MIN_WIDTH = 300;
const MIN_HEIGHT = 200;
const MIN_AREA = 60000;
const MIN_ASPECT_RATIO = 0.5;
const MAX_ASPECT_RATIO = 3.0;
const IMAGE_ID_PREFIX = "article";

module.exports = {
  newsCentreMinConnections,
  newsCentreMaxConnections,
  newsCentreDbUser,
  newsCentreDbPassword,
  newsCentreDbHost,
  newsCentreDbPort,
  newsCentreDbName,
  newsCentreDbSchema,
  INSERT_NEWS_ARTICLE,
  CHECK_NEWS_URL_EXISTS_QUERY,
  PROJECT_ID,
  BUCKET_NAME,
  BUCKET_FOLDER,
  CDN_URL,
  ALLOWED_EXTENSIONS,
  MIN_WIDTH,
  MIN_HEIGHT,
  MIN_AREA,
  MIN_ASPECT_RATIO,
  MAX_ASPECT_RATIO,
  IMAGE_ID_PREFIX,
  VESSEL_TYPE,
  VESSEL_TYPE_GROUP,
  PROMPT_TEMPLATE,
  TEMP_FILE_DIR,
  TAG_TOPIC_NAME,
  GET_ALL_CATEGORY_QUERY,
  GET_ALL_ORGANISATION_QUERY,
  GET_ALL_REGION_QUERY,
  META_DATA_URL,
  ENVIRONMENT,
  GEMINI_API_URL,
  primaryKeywords,
  secondaryKeywords,
  TWITTER_USERNAMES,
  BANNED_WORDS,
  SLACK_WEBHOOK_URL,
  TWITTER_API_BASE,
  TWITTER_AUTH_TOKEN,
  TWITTER_SOURCE_ID,
  INTERVAL_MINUTES,
  INSERT_TWITTER_PULL_QUERY,
  GET_LAST_SEEN_TWEET_ID_QUERY,
  INSERT_OR_UPDATE_MUTED_COMBO,
  RESET_EXPIRED_MUTED_COMBOS,
  GET_MUTED_VESSELS_QUERY,
  GET_TOTAL_PULLS_SINCE_STARTDATE,
  MONTHLY_TARGET_PULLS,
  TWITTER_PULL_START_DAY,
  API_KEYS,
};

