# Node.js dependencies
node_modules/
package-lock.json
yarn.lock

# Environment variables
.env
.env.*
key.json

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Build and distribution files
dist/
build/
.build/
coverage/

# IDE and editor settings
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.iml
*.iws
.idea_modules/
*.suo
*.ntvs*
*.njsproj
*.sln

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Crawlee
storage/

# Temporary files
*.swp
*.swo

# TypeScript cache
*.tsbuildinfo

# Instrumented libs (e.g., jscoverage/JSCover)
lib-cov/

# Output of 'npm pack'
*.tgz

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Yarn Integrity file
.yarn-integrity

# Google Cloud Functions
.gcloudignore

# Test and CI/CD
test/
# *.test.js
.gitlab-ci.yml

# SonarQube
.sonar/
.sonarqube/
sonar-project.properties
.scannerwork/

# Version control
.git/