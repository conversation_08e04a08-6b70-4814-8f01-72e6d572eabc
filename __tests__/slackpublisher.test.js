jest.resetModules(); // Clear modules before each test
jest.mock('axios');
jest.mock('../utils/logger');

const axios = require('axios');
const getLogger = require('../utils/logger');

describe('slackPublisher.js full coverage', () => {
  let sendSlackMessage;
  const mockLogger = {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    getLogger.mockReturnValue(mockLogger);
  });

  it('should skip sending if SLACK_WEBHOOK_URL is missing', async () => {
    jest.doMock('../utils/constants', () => ({ SLACK_WEBHOOK_URL: '' }));
    sendSlackMessage = require('../utils/slackPublisher').sendSlackMessage;

    await sendSlackMessage('No webhook URL');

    expect(mockLogger.warn).toHaveBeenCalledWith(
      'Slack webhook URL is not configured. Skipping Slack notification.'
    );
    expect(axios.post).not.toHaveBeenCalled();
  });

  it('should send Slack message successfully', async () => {
    jest.resetModules(); // Important to reset module cache
  
    jest.doMock('../utils/constants', () => ({
      SLACK_WEBHOOK_URL: 'https://dummy.url'
    }));
  
    const axios = require('axios');
    const getLogger = require('../utils/logger');
    const mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };
    getLogger.mockReturnValue(mockLogger);
    axios.post.mockResolvedValueOnce({});
  
    const { sendSlackMessage } = require('../utils/slackPublisher');
  
    await sendSlackMessage('Message OK');
  
    expect(mockLogger.debug).toHaveBeenCalledWith('Sending Slack message');
    expect(axios.post).toHaveBeenCalledWith('https://dummy.url', { text: 'Message OK' });
    expect(mockLogger.info).toHaveBeenCalledWith('Successfully sent message to Slack');
  });

  it('should log error if Slack message fails to send', async () => {
    jest.resetModules(); // Reset module registry
  
    jest.doMock('../utils/constants', () => ({
      SLACK_WEBHOOK_URL: 'https://dummy.url'
    }));
  
    const axios = require('axios');
    const getLogger = require('../utils/logger');
    const mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };
    getLogger.mockReturnValue(mockLogger);
    axios.post.mockRejectedValueOnce(new Error('Post failed'));
  
    const { sendSlackMessage } = require('../utils/slackPublisher');
  
    await sendSlackMessage('Failing message');
  
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining('Failed to send Slack message')
    );
  });
  
});
