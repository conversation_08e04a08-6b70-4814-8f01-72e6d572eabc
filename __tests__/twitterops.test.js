const { fetchAndProcessTweets } = require('../utils/twitterProcessor');
const twitterClient = require('../utils/twitterClient');
const newsDbManager = require('../utils/newsDbManager');
const modelIntegrator = require('../utils/modelIntegrator');
const mutedComboManager = require('../utils/mutedComboManager');
const publisher = require('../utils/publisher');

jest.mock('../utils/slackPublisher', () => ({
  sendSlackMessage: jest.fn(),
  sendMonthlyPullsAlertToSlack: jest.fn(),
}));

const slackPublisher = require('../utils/slackPublisher');

jest.mock('../utils/twitterClient');
jest.mock('../utils/newsDbManager');
jest.mock('../utils/modelIntegrator');
jest.mock('../utils/publisher');
jest.mock('../utils/mutedComboManager');

const mockTweet = (
  id,
  text = 'Ferry explosion',
  authorId = '123',
  createdAt = new Date().toISOString(),
  images = []
) => ({
  id,
  text,
  author_id: authorId,
  created_at: createdAt,
  images,
});

describe('Twitter Lambda - Edge and Time-Based Scenarios', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mutedComboManager.getMutedVessels.mockResolvedValue([]);
    mutedComboManager.insertOrUpdateMutedCombo.mockResolvedValue(1);
    mutedComboManager.resetExpiredMutedCombos.mockResolvedValue(true);
  });
  
  it('should not trigger Slack alert due to clock skew even if time is near 10:00 IST', async () => {
    const RealDate = Date;
    global.Date = class extends RealDate {
      constructor() {
        super();
        return new RealDate('2025-05-26T04:29:30Z'); // 9:59:30 IST
      }
    };

    twitterClient.searchTweets.mockResolvedValue({ data: [] });
    await fetchAndProcessTweets();
    expect(slackPublisher.sendMonthlyPullsAlertToSlack).not.toHaveBeenCalled();
    global.Date = RealDate;
  });

  it('should insert tweet but fail during publishMessage', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('101')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([
      {
        is_relevant: true,
        vessels: [
          {
            vessel_name: 'TestShip',
            categories: ['explosion'],
            organizations: ['Org1'],
            region: ['R1'],
          },
        ],
      },
    ]);
    publisher.publishMessage.mockRejectedValue(new Error('PubSub error'));

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
    expect(publisher.publishMessage).toHaveBeenCalled();
  });

  it('should handle DB error during combo muting update', async () => {
    mutedComboManager.insertOrUpdateMutedCombo.mockRejectedValue(new Error('DB failure'));
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('102')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([
      {
        is_relevant: true,
        vessels: [{ vessel_name: 'MuteErrShip', categories: ['explosion'] }],
      },
    ]);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should throw memory warning when tweet batch is large', async () => {
    const largeBatch = new Array(500).fill(null).map((_, i) => mockTweet(`${200 + i}`));
    twitterClient.searchTweets.mockResolvedValue({ data: largeBatch });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([
      {
        is_relevant: true,
        vessels: [{ vessel_name: 'BigBatchShip', categories: ['explosion'] }],
      },
    ]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });
});
