require('dotenv').config();
const path = require('path');
const fs = require('fs');

describe('constants.js full coverage', () => {
  let constants;

  beforeAll(() => {
    process.env.NEWS_CENTRE_MIN_CONNECTIONS = '1';
    process.env.NEWS_CENTRE_MAX_CONNECTIONS = '5';
    process.env.NEWS_CENTRE_DB_USER = 'user';
    process.env.NEWS_CENTRE_DB_PASSWORD = 'pass';
    process.env.NEWS_CENTRE_DB_HOST = 'localhost';
    process.env.NEWS_CENTRE_DB_PORT = '5432';
    process.env.NEWS_CENTRE_DB_NAME = 'newsdb';
    process.env.NEWS_CENTRE_DB_SCHEMA = 'news_centre';
    process.env.PROJECT_ID = 'test-project';
    process.env.BUCKET_NAME = 'test-bucket';
    process.env.BUCKET_FOLDER = 'folder';
    process.env.CDN_URL = 'https://cdn.example.com';
    process.env.VESSEL_TYPE = 'Cargo,Passenger';
    process.env.VESSEL_TYPE_GROUP = 'Tankers';
    process.env.SLACK_WEBHOOK_URL = 'https://slack.webhook';
    process.env.TEMP_FILE_DIR = '/tmp';
    process.env.ENVIRONMENT = 'dev';
    process.env.TAG_TOPIC_NAME = 'tag-topic';
    process.env.GEMINI_API_KEY_1 = 'key1';
    process.env.GEMINI_API_KEY_2 = 'key2';
    process.env.TWITTER_API_BASE = 'https://twitter.api';
    process.env.TWITTER_AUTH_TOKEN = 'token';
    process.env.TWITTER_SOURCE_ID = 'source_id';
    process.env.INTERVAL_MINUTES = '30';
    process.env.MONTHLY_TARGET_PULLS = '500';
    process.env.TWITTER_PULL_START_DAY = '01';

    constants = require('../utils/constants');
  });

  it('should have all required environment-based constants defined', () => {
    expect(constants.newsCentreMinConnections).toBe(1);
    expect(constants.newsCentreMaxConnections).toBe(5);
    expect(constants.newsCentreDbUser).toBe('user');
    expect(constants.newsCentreDbPassword).toBe('pass');
    expect(constants.newsCentreDbHost).toBe('localhost');
    expect(constants.newsCentreDbPort).toBe(5432);
    expect(constants.newsCentreDbName).toBe('newsdb');
    expect(constants.newsCentreDbSchema).toBe('news_centre');
    expect(constants.PROJECT_ID).toBe('test-project');
    expect(constants.BUCKET_NAME).toBe('test-bucket');
    expect(constants.BUCKET_FOLDER).toBe('folder');
    expect(constants.CDN_URL).toBe('https://cdn.example.com');
    expect(constants.VESSEL_TYPE).toBe('Cargo,Passenger');
    expect(constants.VESSEL_TYPE_GROUP).toBe('Tankers');
    expect(constants.SLACK_WEBHOOK_URL).toBe('https://slack.webhook');
    expect(constants.TEMP_FILE_DIR).toBe('/tmp');
    expect(constants.ENVIRONMENT).toBe('dev');
    expect(constants.TAG_TOPIC_NAME).toBe('tag-topic');
    expect(constants.API_KEYS).toEqual(['key1', 'key2']);
    expect(constants.TWITTER_API_BASE).toBe('https://twitter.api');
    expect(constants.TWITTER_AUTH_TOKEN).toBe('token');
    expect(constants.TWITTER_SOURCE_ID).toBe('source_id');
    expect(constants.INTERVAL_MINUTES).toBe('30');
    expect(constants.MONTHLY_TARGET_PULLS).toBe('500');
    expect(constants.TWITTER_PULL_START_DAY).toBe('01');
  });

  it('should have valid SQL query strings and static definitions', () => {
    expect(typeof constants.INSERT_NEWS_ARTICLE).toBe('string');
    expect(typeof constants.CHECK_NEWS_URL_EXISTS_QUERY).toBe('string');
    expect(typeof constants.GET_ALL_CATEGORY_QUERY).toBe('string');
    expect(typeof constants.GET_ALL_ORGANISATION_QUERY).toBe('string');
    expect(typeof constants.GET_ALL_REGION_QUERY).toBe('string');
    expect(typeof constants.PROMPT_TEMPLATE).toBe('string');
    expect(constants.PROMPT_TEMPLATE).toContain('{news_title}');
    expect(Array.isArray(constants.primaryKeywords)).toBe(true);
    expect(Array.isArray(constants.secondaryKeywords)).toBe(true);
    expect(Array.isArray(constants.TWITTER_USERNAMES)).toBe(true);
    expect(Array.isArray(constants.BANNED_WORDS)).toBe(true);
    expect(constants.ALLOWED_EXTENSIONS.test('file.png')).toBe(true);
  });

  it('should contain correct numeric/image processing constants', () => {
    expect(constants.MIN_WIDTH).toBeGreaterThan(0);
    expect(constants.MIN_HEIGHT).toBeGreaterThan(0);
    expect(constants.MIN_AREA).toBeGreaterThan(0);
    expect(constants.MIN_ASPECT_RATIO).toBeGreaterThan(0);
    expect(constants.MAX_ASPECT_RATIO).toBeGreaterThan(1);
    expect(typeof constants.IMAGE_ID_PREFIX).toBe('string');
  });

  it('should contain expected twitter tracking queries', () => {
    expect(typeof constants.INSERT_TWITTER_PULL_QUERY).toBe('string');
    expect(typeof constants.GET_LAST_SEEN_TWEET_ID_QUERY).toBe('string');
    expect(typeof constants.INSERT_OR_UPDATE_MUTED_COMBO).toBe('string');
    expect(typeof constants.RESET_EXPIRED_MUTED_COMBOS).toBe('string');
    expect(typeof constants.GET_MUTED_VESSELS_QUERY).toBe('string');
    expect(typeof constants.GET_TOTAL_PULLS_SINCE_STARTDATE).toBe('string');
  });
});
