const mockLogger = {
  info: jest.fn(),
  error: jest.fn()
};

jest.mock('../utils/logger', () => () => mockLogger);

jest.mock('../utils/twitterScheduler', () => ({
  runTwitterFetch: jest.fn()
}));

const { runTwitterFetch } = require('../utils/twitterScheduler');
const { handler } = require('../index');

describe('index.js – Cloud Function handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call runTwitterFetch and log success', async () => {
    runTwitterFetch.mockResolvedValueOnce();
    await handler({});
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Triggered by Cloud Scheduler – Starting Twitter fetch'
    );
    expect(runTwitterFetch).toHaveBeenCalled();
    expect(mockLogger.info).toHaveBeenCalledWith('Completed Twitter fetch');
  });

  it('should log and throw error if runTwitterFetch fails', async () => {
    const error = new Error('Boom');
    runTwitterFetch.mockRejectedValueOnce(error);
    await expect(handler({})).rejects.toThrow('Boom');
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Triggered by Cloud Scheduler – Starting Twitter fetch'
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      `❌ Error during Twitter fetch: ${error.message}`
    );
  });
});
