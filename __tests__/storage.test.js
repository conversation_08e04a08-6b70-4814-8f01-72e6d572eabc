const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  error: jest.fn()
};

const mockFile = {
  save: jest.fn()
};

const mockBucket = {
  file: jest.fn(() => mockFile)
};

const mockStorage = {
  bucket: jest.fn(() => mockBucket)
};

jest.mock('../utils/logger', () => () => mockLogger);
jest.mock('../utils/constants', () => ({
  BUCKET_NAME: 'test-bucket',
  BUCKET_FOLDER: 'test-folder',
  CDN_URL: 'https://cdn.example.com/'
}));
jest.mock('@google-cloud/storage', () => ({
  Storage: jest.fn(() => mockStorage)
}));
jest.mock('path', () => ({
  basename: jest.fn()
}));

global.fetch = jest.fn();
global.URL = jest.fn();

const { uploadImageToGCS } = require('../utils/storage');

describe('uploadImageToGCS', () => {
  const testImageUrl = 'https://example.com/test-image.jpg';
  const testArticleId = 'article-123';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should successfully upload image and return CDN URL', async () => {
    const mockArrayBuffer = new ArrayBuffer(8);
    const mockResponse = {
      arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer),
      headers: {
        get: jest.fn().mockReturnValue('image/jpeg')
      }
    };

    global.fetch.mockResolvedValue(mockResponse);
    global.URL.mockImplementation((url) => ({
      pathname: '/test-image.jpg'
    }));

    const path = require('path');
    path.basename.mockReturnValue('test-image.jpg');

    mockFile.save.mockResolvedValue();

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBe('https://cdn.example.com/test-folder/article-123-test-image.jpg');
    expect(mockLogger.debug).toHaveBeenCalledWith(
      `[uploadImageToGCS] Starting upload for articleId=${testArticleId}, imageUrl=${testImageUrl}`
    );
    expect(mockLogger.info).toHaveBeenCalledWith(
      '[uploadImageToGCS] Upload successful. GCS URL: https://cdn.example.com/test-folder/article-123-test-image.jpg'
    );
    expect(mockFile.save).toHaveBeenCalledWith(
      Buffer.from(mockArrayBuffer),
      { metadata: { contentType: 'image/jpeg' } }
    );
  });

  it('should handle fetch error and return null', async () => {
    global.fetch.mockRejectedValue(new Error('Network error'));

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBeNull();
    expect(mockLogger.error).toHaveBeenCalledWith(
      `[uploadImageToGCS] Failed to upload image for articleId=${testArticleId}, imageUrl=${testImageUrl}`
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      '[uploadImageToGCS] Error details: Network error'
    );
  });

  it('should handle arrayBuffer error and return null', async () => {
    const mockResponse = {
      arrayBuffer: jest.fn().mockRejectedValue(new Error('Buffer error')),
      headers: {
        get: jest.fn().mockReturnValue('image/jpeg')
      }
    };

    global.fetch.mockResolvedValue(mockResponse);

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBeNull();
    expect(mockLogger.error).toHaveBeenCalledWith(
      `[uploadImageToGCS] Failed to upload image for articleId=${testArticleId}, imageUrl=${testImageUrl}`
    );
  });

  it('should handle GCS upload error and return null', async () => {
    const mockArrayBuffer = new ArrayBuffer(8);
    const mockResponse = {
      arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer),
      headers: {
        get: jest.fn().mockReturnValue('image/png')
      }
    };

    global.fetch.mockResolvedValue(mockResponse);
    global.URL.mockImplementation((url) => ({
      pathname: '/another-image.png'
    }));

    const path = require('path');
    path.basename.mockReturnValue('another-image.png');

    mockFile.save.mockRejectedValue(new Error('GCS upload failed'));

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBeNull();
    expect(mockLogger.error).toHaveBeenCalledWith(
      `[uploadImageToGCS] Failed to upload image for articleId=${testArticleId}, imageUrl=${testImageUrl}`
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      '[uploadImageToGCS] Error details: GCS upload failed'
    );
  });

  it('should handle special characters in filename', async () => {
    const mockArrayBuffer = new ArrayBuffer(8);
    const mockResponse = {
      arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer),
      headers: {
        get: jest.fn().mockReturnValue('image/gif')
      }
    };

    global.fetch.mockResolvedValue(mockResponse);
    global.URL.mockImplementation((url) => ({
      pathname: '/special image with spaces & symbols.gif'
    }));

    const path = require('path');
    path.basename.mockReturnValue('special image with spaces & symbols.gif');

    mockFile.save.mockResolvedValue();

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBe('https://cdn.example.com/test-folder/article-123-special%20image%20with%20spaces%20%26%20symbols.gif');
    expect(mockBucket.file).toHaveBeenCalledWith('test-folder/article-123-special%20image%20with%20spaces%20%26%20symbols.gif');
  });

  it('should log debug information during successful upload', async () => {
    const mockArrayBuffer = new ArrayBuffer(16);
    const mockResponse = {
      arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer),
      headers: {
        get: jest.fn().mockReturnValue('image/webp')
      }
    };

    global.fetch.mockResolvedValue(mockResponse);
    global.URL.mockImplementation((url) => ({
      pathname: '/debug-test.webp'
    }));

    const path = require('path');
    path.basename.mockReturnValue('debug-test.webp');

    mockFile.save.mockResolvedValue();

    await uploadImageToGCS(testImageUrl, testArticleId);

    expect(mockLogger.debug).toHaveBeenCalledWith(
      `[uploadImageToGCS] Downloaded image, Content-Type=image/webp, Byte Length=16`
    );
    expect(mockLogger.debug).toHaveBeenCalledWith(
      '[uploadImageToGCS] Original filename: debug-test.webp'
    );
    expect(mockLogger.debug).toHaveBeenCalledWith(
      '[uploadImageToGCS] Safe GCS filename: test-folder/article-123-debug-test.webp'
    );
  });

  it('should handle missing content-type header', async () => {
    const mockArrayBuffer = new ArrayBuffer(8);
    const mockResponse = {
      arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer),
      headers: {
        get: jest.fn().mockReturnValue(null)
      }
    };

    global.fetch.mockResolvedValue(mockResponse);
    global.URL.mockImplementation((url) => ({
      pathname: '/no-content-type.jpg'
    }));

    const path = require('path');
    path.basename.mockReturnValue('no-content-type.jpg');

    mockFile.save.mockResolvedValue();

    const result = await uploadImageToGCS(testImageUrl, testArticleId);

    expect(result).toBe('https://cdn.example.com/test-folder/article-123-no-content-type.jpg');
    expect(mockFile.save).toHaveBeenCalledWith(
      Buffer.from(mockArrayBuffer),
      { metadata: { contentType: null } }
    );
  });
});
