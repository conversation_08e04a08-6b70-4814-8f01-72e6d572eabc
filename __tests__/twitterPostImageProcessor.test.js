const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

jest.mock('../utils/logger', () => {
  return () => mockLogger;
});

jest.mock('../utils/storage');
jest.mock('axios');
jest.mock('image-size');

const { processTweetImages, getMostRelevantImage } = require('../utils/twitterPostImageProcessor');
const { uploadImageToGCS } = require('../utils/storage');
const imageSize = require('image-size');
const axios = require('axios');

const dummyArticleId = 'dummy-article-id';

describe('Tweet Image Processing', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('Relevant image present and uploaded successfully', async () => {
        const imageUrl = 'https://image.com/image.jpg';
        axios.get.mockResolvedValue({ data: 'imageBuffer' });
        imageSize.mockReturnValue({ width: 800, height: 600 });
        uploadImageToGCS.mockResolvedValue('https://gcs.com/image.jpg');

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual(['https://gcs.com/image.jpg']);
        expect(uploadImageToGCS).toHaveBeenCalledWith(imageUrl, dummyArticleId);
    });

    it('Tweet has no image', async () => {
        const result = await processTweetImages([], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Image type is unsupported (e.g., .svg)', async () => {
        const imageUrl = 'https://image.com/image.svg';
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Multiple images but all are irrelevant', async () => {
        const imageUrls = ['https://img1.jpg', 'https://img2.jpg'];
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 5, height: 5 }); // Too small

        const result = await processTweetImages(imageUrls, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Image has invalid dimensions (1x1 tracking pixel)', async () => {
        const imageUrl = 'https://tracker.com/pixel.jpg';
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 1, height: 1 });

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });



    it('Should handle axios error when fetching image dimensions', async () => {
        const imageUrl = 'https://image.com/broken.jpg';
        axios.get.mockRejectedValue(new Error('Network error'));
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getImageDimensions] Failed for URL: https://image.com/broken.jpg | Error: Network error')
        );
    });

    it('Should handle null imageUrls parameter', async () => {
        const result = await processTweetImages(null, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.debug).toHaveBeenCalledWith(
            expect.stringContaining('[processTweetImages] No images provided for articleId=dummy-article-id')
        );
    });

    it('Should handle undefined imageUrls parameter', async () => {
        const result = await processTweetImages(undefined, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Should handle non-array imageUrls parameter', async () => {
        const result = await processTweetImages('not-an-array', dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Should handle image with null dimensions', async () => {
        const imageUrl = 'https://image.com/corrupted.jpg';
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue(null); // Corrupted image

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.debug).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to missing dimensions: https://image.com/corrupted.jpg')
        );
    });

    it('Should handle image with bad aspect ratio (too wide)', async () => {
        const imageUrl = 'https://image.com/banner.jpg';
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 1200, height: 250 });
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to bad aspect ratio: 4.80 for URL: https://image.com/banner.jpg')
        );
    });

    it('Should handle image with bad aspect ratio (too tall)', async () => {
        const imageUrl = 'https://image.com/vertical.jpg';
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 300, height: 800 });
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to bad aspect ratio: 0.38 for URL: https://image.com/vertical.jpg')
        );
    });

    it('Should handle uploadImageToGCS returning null', async () => {
        const imageUrl = 'https://image.com/image.jpg';
        axios.get.mockResolvedValue({ data: 'imageBuffer' });
        imageSize.mockReturnValue({ width: 800, height: 600 });
        uploadImageToGCS.mockResolvedValue(null);
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).toHaveBeenCalledWith(imageUrl, dummyArticleId);
        expect(mockLogger.error).toHaveBeenCalledWith(
            expect.stringContaining('[processTweetImages] Upload failed for image: https://image.com/image.jpg')
        );
    });

    it('Should handle uploadImageToGCS throwing an exception', async () => {
        const imageUrl = 'https://image.com/image.jpg';
        axios.get.mockResolvedValue({ data: 'imageBuffer' });
        imageSize.mockReturnValue({ width: 800, height: 600 });
        uploadImageToGCS.mockRejectedValue(new Error('Storage error'));
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).toHaveBeenCalledWith(imageUrl, dummyArticleId);
        expect(mockLogger.error).toHaveBeenCalledWith(
            expect.stringContaining('[processTweetImages] Exception uploading image: https://image.com/image.jpg | Error: Storage error')
        );
    });

    it('Should select image with largest area when multiple valid images exist', async () => {
        const imageUrls = [
            'https://image.com/small.jpg',
            'https://image.com/large.jpg',
            'https://image.com/medium.jpg'
        ];

        axios.get.mockImplementation((url) => {
            return Promise.resolve({ data: 'buffer' });
        });

        imageSize.mockImplementation((data) => {
            const callCount = imageSize.mock.calls.length;
            if (callCount === 1) return { width: 400, height: 300 };
            if (callCount === 2) return { width: 800, height: 600 };
            if (callCount === 3) return { width: 600, height: 400 };
        });

        uploadImageToGCS.mockResolvedValue('https://gcs.com/large.jpg');

        const result = await processTweetImages(imageUrls, dummyArticleId);
        expect(result).toEqual(['https://gcs.com/large.jpg']);
        expect(uploadImageToGCS).toHaveBeenCalledWith('https://image.com/large.jpg', dummyArticleId);
        expect(mockLogger.info).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Final selected image: https://image.com/large.jpg')
        );
    });

    it('Should handle URLs with query parameters', async () => {
        const imageUrl = 'https://image.com/image.jpg?v=123&size=large';
        axios.get.mockResolvedValue({ data: 'imageBuffer' });
        imageSize.mockReturnValue({ width: 800, height: 600 });
        uploadImageToGCS.mockResolvedValue('https://gcs.com/image.jpg');

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual(['https://gcs.com/image.jpg']);
        expect(uploadImageToGCS).toHaveBeenCalledWith('https://image.com/image.jpg', dummyArticleId);
    });

    it('Should handle empty imageUrls array', async () => {
        const result = await processTweetImages([], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.debug).toHaveBeenCalledWith(
            expect.stringContaining('[processTweetImages] No images provided for articleId=dummy-article-id')
        );
    });

    it('Should handle case where getMostRelevantImage receives empty array after filtering', async () => {
        const imageUrls = [
            'https://image.com/unsupported.svg',
        ];
        const result = await processTweetImages(imageUrls, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to extension: https://image.com/unsupported.svg')
        );
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] No relevant image found.')
        );
    });

    it('Should handle getMostRelevantImage with null input', async () => {
        const result1 = await processTweetImages(null, dummyArticleId);
        expect(result1).toEqual([]);
        const result2 = await processTweetImages(undefined, dummyArticleId);
        expect(result2).toEqual([]);
        const result3 = await processTweetImages([], dummyArticleId);
        expect(result3).toEqual([]);
        expect(mockLogger.debug).toHaveBeenCalledWith(
            expect.stringContaining('[processTweetImages] No images provided for articleId=dummy-article-id')
        );
    });

    it('Should handle getMostRelevantImage when all images are filtered out', async () => {
        const imageUrls = [
            'https://image.com/tiny.jpg',
            'https://image.com/unsupported.gif'
        ];
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 10, height: 10 });
        const result = await processTweetImages(imageUrls, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to extension')
        );
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] Skipping due to small size')
        );
        expect(mockLogger.warn).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] No relevant image found.')
        );
    });

    // DIRECT TEST FOR UNCOVERED LINES 38-39
    it('Should cover lines 38-39 - getMostRelevantImage with empty array', async () => {
        // Directly call getMostRelevantImage with empty array to trigger lines 38-39
        const result = await getMostRelevantImage([]);
        expect(result).toBeNull();
        expect(mockLogger.debug).toHaveBeenCalledWith(
            expect.stringContaining('[getMostRelevantImage] No images to evaluate.')
        );
    });
});
