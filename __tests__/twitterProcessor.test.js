const { fetchAndProcessTweets, buildQueryString } = require('../utils/twitterProcessor');
const twitterClient = require('../utils/twitterClient');
const newsDbManager = require('../utils/newsDbManager');
const modelIntegrator = require('../utils/modelIntegrator');
const slackPublisher = require('../utils/slackPublisher');
const mutedComboManager = require('../utils/mutedComboManager');
const publisher = require('../utils/publisher');
const twitterPostImageProcessor = require('../utils/twitterPostImageProcessor');
const constants = require('../utils/constants');


jest.mock('../utils/twitterClient');
jest.mock('../utils/newsDbManager');
jest.mock('../utils/modelIntegrator');
jest.mock('../utils/slackPublisher');
jest.mock('../utils/mutedComboManager');
jest.mock('../utils/publisher');
jest.mock('../utils/twitterPostImageProcessor');

const mockTweet = (id, text = 'Ferry explosion', authorId = '123', createdAt = new Date().toISOString(), images = []) => ({
  id,
  text,
  author_id: authorId,
  created_at: createdAt,
  images
});

describe('Twitter Lambda - Core Tweet Processing Scenarios', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    constants.primaryKeywords = ['Ferry'];
    constants.secondaryKeywords = ['explosion'];
    constants.TWITTER_USERNAMES = ['shiptracker', 'marinewatch'];
    constants.BANNED_WORDS = ["damn", "hell", "fuck", "shit"];

    mutedComboManager.getMutedVessels.mockResolvedValue([]);
    mutedComboManager.resetExpiredMutedCombos.mockResolvedValue(true);
    mutedComboManager.insertOrUpdateMutedCombo.mockResolvedValue(1);
    twitterPostImageProcessor.processTweetImages.mockResolvedValue([]);
    newsDbManager.calculatePullsPerInterval.mockResolvedValue(10);
  });

  it('should ingest all tweets', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('1'), mockTweet('2')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Evergreen', categories: ['explosion'], organizations: ['Org1'], region: ['Region1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(2);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(2);
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Ingested: *2*'));
  });

  it('should skip if no tweets pulled', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('pulled 0 tweets'));
  });

  it('should insert one and filter one tweet', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('5'), mockTweet('6')] });
    newsDbManager.checkArticleExists.mockImplementation((url) =>
      url.includes('5') ? false : true
    );
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Evergreen', categories: ['explosion'], organizations: ['Org1'], region: ['Region1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(1);
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Ingested: *1*'));
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Filtered Out: *1*'));
  });

  it('should treat tweets with same content but different IDs as unique', async () => {
    const tweetA = mockTweet('7', 'Ferry explosion');
    const tweetB = mockTweet('8', 'Ferry explosion');

    twitterClient.searchTweets.mockResolvedValue({ data: [tweetA, tweetB] });
    newsDbManager.checkArticleExists.mockResolvedValueOnce(false).mockResolvedValueOnce(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Ship1', categories: ['explosion'], organizations: ['Org'], region: ['Region'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(2);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(2);
  });

  it('should skip second tweet with duplicate ID', async () => {
    const tweet = mockTweet('9');

    twitterClient.searchTweets.mockResolvedValue({ data: [tweet, tweet] });
    newsDbManager.checkArticleExists.mockResolvedValueOnce(false).mockResolvedValueOnce(true);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'VesselDup', categories: ['explosion'], organizations: ['OrgDup'], region: ['RegionDup'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(1);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(1);
  });

  it('should filter duplicate tweet by URL', async () => {
    const tweet = mockTweet('10');
    twitterClient.searchTweets.mockResolvedValue({ data: [tweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter muted vessel-keyword combos', async () => {
    mutedComboManager.insertOrUpdateMutedCombo.mockResolvedValue(11);
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('11')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'MutedShip', categories: ['explosion'], organizations: [], region: [] }]
    }]);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing primary keyword', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('13', 'only explosion')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing secondary keyword', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('14', 'only Ferry')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing both keywords', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('15', 'random noise')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets with unmatched category', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('16')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'VesselX', categories: ['explosion'] }]
    }]);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'xyz' }]);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter empty vessel array in model response', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('17')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{ is_relevant: true, vessels: [] }]);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should ingest tweet with keywords in hashtags', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('18', '#Ferry #explosion near dock')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'HashShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should detect keywords with casing differences', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('19', 'FeRrY EXPLOSION')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'CaseShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should detect keywords in text with emojis', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('20', 'Ferry 💥 explosion')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'EmojiShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should ignore alt text-only image keywords', async () => {
    const imageWithAlt = [{ url: 'image.jpg', alt_text: 'Ferry explosion' }];
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('22', 'random text', '123', new Date().toISOString(), imageWithAlt)] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should include mocked usernames and banned words in the query', async () => {
    const testUsernames = ['mercoglianos', 'BartGonnissen'];
    const testBannedWords = ['hell'];

    const query = await buildQueryString({
      usernames: testUsernames,
      bannedWords: testBannedWords,
      getMutedVesselsFn: async () => []
    });

    expect(query).toContain('from:mercoglianos OR from:BartGonnissen');
    expect(query).toContain('-is:retweet');
    expect(query).toContain('-hell');
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should handle Twitter query string exceeding 512 characters gracefully', async () => {
    constants.TWITTER_USERNAMES = new Array(100).fill('username_long_enough_to_exceed_query_limit');
    const query = await buildQueryString();
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should ingest tweet containing special characters in text', async () => {
    const specialCharTweet = mockTweet('special1', 'Ferry explosion 🚢🔥💥');
    twitterClient.searchTweets.mockResolvedValue({ data: [specialCharTweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{ is_relevant: true, vessels: [{ vessel_name: 'SpecialShip', categories: ['explosion'] }] }]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should handle Twitter API 500 Internal Server Error gracefully', async () => {
    twitterClient.searchTweets.mockRejectedValue(new Error('Internal Server Error'));
    await expect(fetchAndProcessTweets()).resolves.not.toThrow();
    expect(slackPublisher.sendSlackMessage).not.toHaveBeenCalledWith(expect.stringContaining('Ingested'));
  });

  it('should handle Twitter API Auth Token Expired scenario', async () => {
    twitterClient.searchTweets.mockRejectedValue(new Error('401 Unauthorized'));
    await expect(fetchAndProcessTweets()).resolves.not.toThrow();
    expect(slackPublisher.sendSlackMessage).not.toHaveBeenCalled();
  });

  it('should throw error when no Twitter usernames provided (line 16)', async () => {
    jest.resetModules();

    jest.doMock('../utils/constants', () => ({
      ...jest.requireActual('../utils/constants'),
      TWITTER_USERNAMES: []
    }));

    const { buildQueryString: buildQueryStringWithEmptyUsernames } = require('../utils/twitterProcessor');

    await expect(buildQueryStringWithEmptyUsernames()).rejects.toThrow('No Twitter usernames provided for secondary filtering');
  });

  it('should break when query exceeds 512 characters with muted vessels (lines 25-29)', async () => {
    const longVesselNames = Array(50).fill().map((_, i) => `very_long_vessel_name_${i}_that_will_exceed_limit`);
    mutedComboManager.getMutedVessels.mockResolvedValue(longVesselNames);

    const query = await buildQueryString();
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should break when query exceeds 512 characters with banned words (line 39)', async () => {
    jest.resetModules();

    const longBannedWords = Array(100).fill().map((_, i) => `very_long_banned_word_${i}_that_will_exceed_limit_and_make_query_too_long`);

    jest.doMock('../utils/constants', () => ({
      ...jest.requireActual('../utils/constants'),
      BANNED_WORDS: longBannedWords
    }));

    jest.doMock('../utils/mutedComboManager', () => ({
      getMutedVessels: jest.fn().mockResolvedValue([])
    }));

    const { buildQueryString: buildQueryStringWithLongBannedWords } = require('../utils/twitterProcessor');

    const query = await buildQueryStringWithLongBannedWords();
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should log uploaded image message (line 121)', async () => {
    const tweetWithImages = mockTweet('img1', 'Ferry explosion', '123', new Date().toISOString(), ['image1.jpg']);
    twitterClient.searchTweets.mockResolvedValue({ data: [tweetWithImages] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'ImageShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    twitterPostImageProcessor.processTweetImages.mockResolvedValue(['uploaded_image.jpg']);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should handle invalid/empty model response array (lines 141-142)', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('empty1')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should handle invalid model response object (lines 150-151)', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('invalid1')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([null]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter irrelevant tweets (lines 155-156)', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('irrelevant1')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: false,
      vessels: [{ vessel_name: 'IrrelevantShip', categories: ['explosion'] }]
    }]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should handle error in processTweet function (lines 265-266)', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('error1')] });
    newsDbManager.checkArticleExists.mockRejectedValue(new Error('Database connection failed'));

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should log warning for processing time > 2 minutes (line 270)', async () => {
    const originalDateNow = Date.now;
    let callCount = 0;
    Date.now = jest.fn(() => {
      callCount++;
      if (callCount === 1) return 0;
      if (callCount === 2) return 0;
      return 130000;
    });

    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('slow1')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'SlowShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    Date.now = originalDateNow;
  });

  it('should send Slack alert at 10:00 AM IST (lines 299-300)', async () => {
    const originalDate = global.Date;
    global.Date = class extends Date {
      constructor(...args) {
        if (args.length === 0) {
          return new originalDate('2025-01-15T04:30:00.000Z');
        }
        return new originalDate(...args);
      }
      toLocaleString(...args) {
        if (args[1] && args[1].timeZone === 'Asia/Kolkata') {
          return '1/15/2025, 10:00:00 AM';
        }
        return new originalDate().toLocaleString(...args);
      }
      getHours() {
        return 10;
      }
      getMinutes() {
        return 0;
      }
    };

    twitterClient.searchTweets.mockResolvedValue({ data: [] });
    twitterClient.sendMonthlyPullsAlertToSlack.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(twitterClient.sendMonthlyPullsAlertToSlack).toHaveBeenCalled();

    global.Date = originalDate;
  });

  // Additional tests to achieve 100% branch coverage
  it('should handle missing author_id and created_at fields (lines 128-129)', async () => {
    const tweetWithMissingFields = {
      id: 'missing-fields',
      text: 'Ferry explosion',
      // author_id and created_at are missing
      images: []
    };

    twitterClient.searchTweets.mockResolvedValue({
      data: [tweetWithMissingFields]
    });

    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'TestShip', categories: ['explosion'] }]
    }]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    // Verify that default values were used
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledWith(
      expect.objectContaining({
        author: 'Twitter User', // Default value when author_id is missing
        publish_date: expect.any(String) // Default value when created_at is missing
      })
    );
  });

  it('should handle categories as objects vs strings (lines 171-173)', async () => {
    const tweet = {
      id: 'category-objects',
      text: 'Ferry explosion',
      author_id: '123',
      created_at: new Date().toISOString(),
      images: []
    };

    twitterClient.searchTweets.mockResolvedValue({ data: [tweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }, { name: 'fire' }]);

    // Model response with mixed category types (objects and strings)
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{
        vessel_name: 'TestShip',
        categories: [
          { name: 'explosion', type: 'INCIDENT' }, // Object format
          'fire' // String format
        ]
      }]
    }]);

    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(publisher.publishMessage).toHaveBeenCalledWith(
      'hull-dev-quest-auto-tagging',
      expect.objectContaining({
        categories: expect.arrayContaining([
          { name: 'explosion', type: 'INCIDENT' },
          { name: 'fire', type: 'UNKNOWN' }
        ])
      })
    );
  });

  it('should handle organizations as objects vs strings (lines 208-229)', async () => {
    const tweet = {
      id: 'org-objects',
      text: 'Ferry explosion',
      author_id: '123',
      created_at: new Date().toISOString(),
      images: []
    };

    twitterClient.searchTweets.mockResolvedValue({ data: [tweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);

    // Model response with mixed organization types (objects and strings)
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{
        vessel_name: 'TestShip',
        categories: ['explosion'],
        organizations: [
          { name: 'Org1', type: 'COMPANY' }, // Object format
          'Org2' // String format
        ]
      }]
    }]);

    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(publisher.publishMessage).toHaveBeenCalledWith(
      'hull-dev-quest-auto-tagging',
      expect.objectContaining({
        organisations: expect.arrayContaining([
          { name: 'Org1', type: 'COMPANY' },
          { name: 'Org2', type: 'UNKNOWN' }
        ])
      })
    );
  });

  it('should handle vessel processing with missing fields (lines 233-238)', async () => {
    const tweet = {
      id: 'vessel-fields',
      text: 'Ferry explosion',
      author_id: '123',
      created_at: new Date().toISOString(),
      images: []
    };

    twitterClient.searchTweets.mockResolvedValue({ data: [tweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);

    // Model response with vessel having missing optional fields
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{
        vessel_name: 'TestShip',
        categories: ['explosion'],
        organizations: ['Org1'],
        // imo, vessel_type, vessel_type_group are missing
      }]
    }]);

    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(publisher.publishMessage).toHaveBeenCalledWith(
      'hull-dev-quest-auto-tagging',
      expect.objectContaining({
        vessels: expect.arrayContaining([
          expect.objectContaining({
            name: 'TestShip',
            imo: null, // Default when missing
            vesselType: '', // Default when missing
            vesselTypeGroup: '' // Default when missing
          })
        ])
      })
    );
  });

  it('should handle undefined tweetData.data (line 309)', async () => {
    // Mock searchTweets to return undefined data
    twitterClient.searchTweets.mockResolvedValue({ data: undefined });
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    // Should handle undefined data gracefully and send appropriate Slack message
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(
      expect.stringContaining('Twitter Engine pulled 0 tweets this cycle')
    );
  });

  it('should handle null tweetData (line 309)', async () => {
    // Mock searchTweets to return null
    twitterClient.searchTweets.mockResolvedValue(null);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    // Should handle null data gracefully and send appropriate Slack message
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(
      expect.stringContaining('Twitter Engine pulled 0 tweets this cycle')
    );
  });

});
