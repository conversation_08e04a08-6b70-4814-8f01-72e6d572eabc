const fetch = require('node-fetch');
const { getIdToken } = require('../utils/gcloud');
const { META_DATA_URL } = require('../utils/constants');

jest.mock('node-fetch');

describe('gcloud.js full coverage', () => {
  const mockResponse = {
    text: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockResolvedValue(mockResponse);
  });

  it('should call fetch with correct URL and headers', async () => {
    const targetUrl = 'target.service.url';
    mockResponse.text.mockResolvedValue('mocked-token');

    const token = await getIdToken(targetUrl);

    expect(fetch).toHaveBeenCalledWith(`${META_DATA_URL}${targetUrl}`, {
      headers: { 'Metadata-Flavor': 'Google' }
    });
    expect(mockResponse.text).toHaveBeenCalled();
    expect(token).toBe('mocked-token');
  });

  it('should throw an error if fetch rejects', async () => {
    const targetUrl = 'fail.url';
    fetch.mockRejectedValueOnce(new Error('Network error'));

    await expect(getIdToken(targetUrl)).rejects.toThrow('Network error');
  });

  it('should throw an error if response.text rejects', async () => {
    const targetUrl = 'fail.text';
    mockResponse.text.mockRejectedValueOnce(new Error('Text conversion failed'));

    await expect(getIdToken(targetUrl)).rejects.toThrow('Text conversion failed');
  });
});