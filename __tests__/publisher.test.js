const mockLogger = {
    error: jest.fn(),
    info: jest.fn()
};

jest.mock('@google-cloud/pubsub', () => ({
    PubSub: jest.fn().mockImplementation(() => ({
        topic: jest.fn().mockReturnThis(),
        publishMessage: jest.fn().mockResolvedValue('mocked-message-id')
    }))
}));

jest.mock('../utils/logger', () => () => mockLogger);

const { publishMessage } = require('../utils/publisher');

describe('publisher.js', () => {
    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    it('should publish message and log info', async () => {
        const result = await publishMessage('test-topic', { key: 'value' });

        expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Publishing message to topic test-topic'));
        expect(result).toBe('mocked-message-id');
    });

    it('should throw error when data contains circular structure (pre-stringify)', async () => {
        const circular = {};
        circular.self = circular;

        await expect(publishMessage('circular-topic', circular)).rejects.toThrow(
            'Converting circular structure to JSON'
        );

        expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should handle PubSub publishMessage error and log error (lines 17-18)', async () => {
        jest.resetModules();

        const mockTopic = {
            publishMessage: jest.fn().mockRejectedValue(new Error('PubSub service unavailable'))
        };

        jest.doMock('@google-cloud/pubsub', () => ({
            PubSub: jest.fn().mockImplementation(() => ({
                topic: jest.fn().mockReturnValue(mockTopic)
            }))
        }));

        const { publishMessage: publishMessageWithError } = require('../utils/publisher');

        await expect(publishMessageWithError('error-topic', { test: 'data' })).rejects.toThrow('PubSub service unavailable');

        expect(mockLogger.error).toHaveBeenCalledWith('Error publishing message: PubSub service unavailable');
        expect(mockLogger.info).toHaveBeenCalledWith('Publishing message to topic error-topic');
    });

});
