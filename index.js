const functions = require("@google-cloud/functions-framework");
const { runTwitterFetch } = require("./utils/twitterScheduler");
const getLogger = require("./utils/logger");

const logger = getLogger();

const handler = async (cloudEvent) => {
  try {
    logger.info("Triggered by Cloud Scheduler – Starting Twitter fetch");
    await runTwitterFetch();
    logger.info("Completed Twitter fetch");
  } catch (error) {
    logger.error(`❌ Error during Twitter fetch: ${error.message}`);
    throw error;
  }
};

functions.cloudEvent("crawlerEngine", handler);

module.exports = { handler };
